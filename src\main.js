import Vue from "vue";
import App from "./App.vue";
import router from "./router";
import store from "./store";

import Vant from "vant";
import { ImagePreview } from "vant";
import { Icon } from "vant";
import "vant/lib/index.css";
import "@/styles/index.scss";
import "@/styles/icon/iconfont.css";
// 移动端适配
import "amfe-flexible";
import "normalize.css";

import "@/permission"; // permission control
import formRules from "./plugins/formRules";

import dayjs from "dayjs";
Vue.prototype.dayjs = dayjs; //可以全局使用dayjs

Vue.prototype.STATIC_URL = process.env.BASE_URL; //可以全局使用dayjs

Vue.use(Vant);
Vue.use(Icon);
Vue.use(formRules);
Vue.config.productionTip = false;
Vue.use(ImagePreview);

new Vue({
  router,
  store,
  render: (h) => h(App),
}).$mount("#app");
