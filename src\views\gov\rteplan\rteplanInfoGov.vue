<!--
  * @date: 2023-08-07
  * @author:fanls
  * @desc:电子运单详情
 -->
<template>
  <div class="main-content">
    <!-- 一单四状态 -->
    <div class="container">
      <ul class="timeline">
        <li :class="{ fali: dataList.transportationStatusCode >= 2 }">
          <div class="check">
            <span :class="{ fatext: dataList.transportationStatusCode >= 1 }"
              >发</span
            >
          </div>
          <div class="contentSecond">
            <b></b>
            <p
              style="font-weight: bold"
              :class="{
                statusActive: dataList.transportationStatusCode >= 1,
              }"
            >
              发车
            </p>
            <p>
              实时位置：<span id="faposition">{{ dataList.goAddr }}</span>
            </p>
            <p>
              备注：<span id="faremark">{{
                dataList.goTm ? "发车提货" : ""
              }}</span>
            </p>
            <p v-show="dataList.transportationStatusCode >= 1">
              操作人：<span id="faperson">{{ dataList.dvNm }}</span>
            </p>
            <p>
              操作时间：<span id="fatime">{{ dataList.goTm }}</span>
            </p>
          </div>
        </li>
        <li :class="{ fali: dataList.transportationStatusCode >= 3 }">
          <div class="check">
            <span :class="{ fatext: dataList.transportationStatusCode >= 2 }"
              >装</span
            >
          </div>
          <div class="contentSecond">
            <b></b>
            <p
              style="font-weight: bold"
              :class="{
                statusActive: dataList.transportationStatusCode >= 2,
              }"
            >
              装货
            </p>
            <p>
              实时位置：<span id="zhuangposition">{{ dataList.loadAddr }}</span>
            </p>
            <p>
              备注：<span id="zhuangremark">{{
                dataList.loadTm ? "装货启运" : ""
              }}</span>
            </p>
            <p v-show="dataList.transportationStatusCode >= 2">
              操作人：<span id="zhuangperson">{{ dataList.dvNm }}</span>
            </p>
            <p>
              操作时间：<span id="zhuangtime">{{ dataList.loadTm }}</span>
            </p>
            <p>实名认证：<span></span></p>
            <p>
              装货重量：<span id="zhuangqty">{{
                dataList.loadActQty ? dataList.loadActQty + "吨" : ""
              }}</span>
            </p>
          </div>
        </li>
        <li :class="{ fali: dataList.transportationStatusCode >= 4 }">
          <div class="check">
            <span :class="{ fatext: dataList.transportationStatusCode >= 3 }"
              >卸</span
            >
          </div>
          <div class="contentSecond">
            <b></b>
            <p
              style="font-weight: bold"
              :class="{
                statusActive: dataList.transportationStatusCode >= 3,
              }"
            >
              卸货
            </p>
            <p>
              实时位置：<span id="xieposition">{{ dataList.unloadAddr }}</span>
            </p>
            <p>
              备注：<span id="xieremark">{{
                dataList.unloadTm ? "卸货" : ""
              }}</span>
            </p>
            <p v-show="dataList.transportationStatusCode >= 3">
              操作人：<span id="xieperson">{{ dataList.dvNm }}</span>
            </p>
            <p>
              操作时间：<span id="xietime">{{ dataList.unloadTm }}</span>
            </p>
            <p>实名认证：<span></span></p>
            <p>
              卸货重量：<span id="xieqty">{{
                dataList.unloadActQty ? data.unloadActQty + "吨" : ""
              }}</span>
            </p>
          </div>
        </li>
        <li
          id="huili"
          :class="{ huili: dataList.transportationStatusCode == -1 }"
        >
          <div class="check">
            <span :class="{ fatext: dataList.transportationStatusCode >= 4 }"
              >回</span
            >
          </div>
          <div class="contentSecond">
            <b></b>
            <p
              style="font-weight: bold"
              :class="{
                statusActive: dataList.transportationStatusCode >= 4,
              }"
            >
              结束
            </p>
            <p>
              实时位置：<span id="endposition">{{ dataList.backAddr }}</span>
            </p>
            <p>
              备注：<span id="endremark">{{
                dataList.backTm ? "结束" : ""
              }}</span>
            </p>
            <p v-show="dataList.transportationStatusCode >= 4">
              操作人：<span id="endperson">{{ dataList.dvNm }}</span>
            </p>
            <p>
              操作时间：<span id="endtime">{{ dataList.backTm }}</span>
            </p>
          </div>
        </li>
        <li
          id="errorli"
          class="errorli"
          v-show="dataList.transportationStatusCode == -1"
        >
          <div class="check">
            <span id="errortext">异</span>
          </div>
          <div class="contentSecond">
            <b></b>
            <p style="font-weight: bold; color: #ff0000">异常结束</p>
            <p>
              实时位置：<span id="errorposition">{{
                dataList.errBackAddr
              }}</span>
            </p>
            <p>备注：<span id="errorremark">异常结束</span></p>
            <p>
              操作人：<span id="errorperson">{{ dataList.dvNm }}</span>
            </p>
            <p>
              操作时间：<span id="errortime">{{ dataList.errBackTm }}</span>
            </p>
          </div>
        </li>
      </ul>
    </div>
    <!-- 运单信息 -->
    <van-collapse v-model="activeName">
      <template>
        <div class="collapse-one" style="">
          <van-field
            readonly
            v-model="dataList.crtTm"
            label="创建时间"
            placeholder="创建时间"
          />
          <van-field
            readonly
            v-model="dataList.updTm"
            label="更新时间"
            placeholder="更新时间"
          />
          <van-field
            readonly
            v-model="dataList.vecDespTm"
            label="起运日期"
            placeholder="起运日期"
          />
          <van-field
            readonly
            v-model="dataList.cd"
            label="运单号"
            placeholder="运单号"
          />
        </div>
      </template>
      <template v-for="(item, index) in activeTitle">
        <van-collapse-item
          class="collapse"
          :icon="item.icon"
          :key="index"
          :title="item.title"
          :name="item.title"
        >
          <div class="basic" v-show="item.title == '承运方'">
            <van-field
              readonly
              v-model="dataList.carrierNm"
              label="单位名称"
              placeholder="单位名称"
              type="textarea"
              rows="1"
              autosize
            />
            <van-field
              readonly
              v-model="dataList.carrierUscCd"
              label="统一信用代码"
              placeholder="统一信用代码"
            />
            <van-field
              readonly
              v-model="dataList.erNm"
              label="负责人"
              placeholder="负责人"
            />
            <van-field
              readonly
              v-model="dataList.erMob"
              label="负责人电话"
              placeholder="负责人电话"
            />
          </div>

          <div class="basic" v-show="item.title == '牵引车'">
            <van-field
              readonly
              v-model="dataList.tracCd"
              label="车牌号(头)"
              placeholder="车牌号(头)"
            />
            <van-field
              readonly
              v-model="dataList.tracOpraLicNo"
              label="道路运输证号"
              placeholder="道路运输证号"
            />
            <van-field
              readonly
              v-model="dataList.tracWeight"
              label="牵引车质量"
              placeholder="牵引车质量"
            />
          </div>

          <div class="basic" v-show="item.title == '挂车'">
            <van-field
              readonly
              v-model="dataList.traiCd"
              label="车牌号(挂)"
              placeholder="车牌号(挂)"
            />
            <van-field
              readonly
              v-model="dataList.traiOpraLicNo"
              label="道路运输证号"
              placeholder="道路运输证号"
            />
            <van-field
              readonly
              v-model="dataList.traiWeight"
              label="挂车核准质量"
              placeholder="挂车核准质量"
            />
          </div>

          <div class="basic" v-show="item.title == '罐体'">
            <van-field
              readonly
              v-model="dataList.tankNum"
              label="罐体编号"
              placeholder="罐体编号"
            />
            <van-field
              readonly
              v-model="dataList.tankVolume"
              label="罐体容积(m³)"
              placeholder="罐体容积(m³)"
            />
          </div>

          <div class="basic" v-show="item.title == '驾驶员'">
            <van-field
              readonly
              v-model="dataList.dvNm"
              label="姓名"
              placeholder="姓名"
            />
            <van-field
              readonly
              v-model="dataList.dvCd"
              label="从业资格证"
              placeholder="从业资格证"
            />
            <van-field
              readonly
              v-model="dataList.dvMob"
              label="联系电话"
              placeholder="联系电话"
            />
          </div>

          <div class="basic" v-show="item.title == '押运员'">
            <van-field
              readonly
              v-model="dataList.scNm"
              label="姓名"
              placeholder="姓名"
            />
            <van-field
              readonly
              v-model="dataList.scCd"
              label="从业资格证"
              placeholder="从业资格证"
            />
            <van-field
              readonly
              v-model="dataList.scMob"
              label="联系电话"
              placeholder="联系电话"
            />
          </div>

          <div class="basic" v-show="item.title == '委托方'">
            <van-field
              readonly
              v-model="dataList.consignorAddr"
              label="单位"
              placeholder="单位"
              type="textarea"
              rows="1"
              autosize
            />
            <van-field
              readonly
              v-model="dataList.consignorTel"
              label="联系方式"
              placeholder="联系方式"
            />
          </div>

          <div class="basic" v-show="item.title == '装货方'">
            <van-field
              readonly
              v-model="dataList.csnorWhseAddr"
              label="企业"
              placeholder="企业"
              type="textarea"
              rows="1"
              autosize
            />
            <van-field
              readonly
              v-model="dataList.csnorWhseDist"
              label="地址"
              placeholder="地址"
              type="textarea"
              rows="1"
              autosize
            />
            <van-field
              readonly
              v-model="dataList.csnorWhseCt"
              label="联系人"
              placeholder="联系人"
            />
            <van-field
              readonly
              v-model="dataList.csnorWhseTel"
              label="联系方式"
              placeholder="联系方式"
            />
          </div>
          <div class="basic" v-show="item.title == '卸货方'">
            <van-field
              readonly
              v-model="dataList.csneeWhseAddr"
              label="企业"
              placeholder="企业"
              type="textarea"
              rows="1"
              autosize
            />
            <van-field
              readonly
              v-model="dataList.csneeWhseDist"
              label="地址"
              placeholder="地址"
              type="textarea"
              rows="1"
              autosize
            />
            <van-field
              readonly
              v-model="dataList.csneeWhseCt"
              label="联系人"
              placeholder="联系人"
            />
            <van-field
              readonly
              v-model="dataList.csneeWhseTel"
              label="联系方式"
              placeholder="联系方式"
            />
          </div>
          <div class="basic" v-show="item.title == '货物信息'">
            <van-field
              readonly
              v-model="dataList.goodsNm"
              label="货品名称"
              placeholder="货品名称"
            />
            <van-field
              readonly
              v-model="dataList.loadQty"
              label="装货数量(吨)"
              placeholder="装货数量(吨)"
            />
            <van-field
              readonly
              v-model="dataList.categroy"
              label="危险货物类别"
              placeholder="危险货物类别"
            />
            <van-field
              readonly
              v-model="dataList.un"
              label="联合国编码"
              placeholder="联合国编码"
            />
          </div>
        </van-collapse-item>
      </template>
    </van-collapse>
    <!-- 二维码  -->
    <div class="footer">
      <div class="text-center" @click="showOverlay">
        <div id="qrcode" align="center"></div>
        <!-- <h4 class="footer-h4">
          使用官方APP扫描二维码核验详情
        </h4> -->
      </div>
      <van-overlay :show="show" @click="show = false">
        <div id="fullscreen" class="fullscreen hidden">
          <div class="text-center code-big">
            <div id="qrcodeBig" align="center"></div>
          </div>
        </div>
      </van-overlay>
    </div>
  </div>
</template>

<script>
import * as $http from "@/apis/rteplan.js";
import QRCode from "qrcodejs2";
// import { getRequestValueByParam } from "@/utils/common";
// let token = getRequestValueByParam('token')

export default {
  data() {
    return {
      activeName: [], //默认展开折叠面板
      activeTitle: [
        { title: "承运方", icon: "wap-home-o" },
        { title: "牵引车", icon: "logistics" },
        { title: "挂车", icon: "logistics" },
        { title: "罐体", icon: "apps-o" },
        { title: "驾驶员", icon: "manager-o" },
        { title: "押运员", icon: "manager-o" },
        { title: "委托方", icon: "apps-o" },
        { title: "装货方", icon: "apps-o" },
        { title: "卸货方", icon: "apps-o" },
        { title: "货物信息", icon: "apps-o" },
      ],
      dataList: {},
      show: false,
      qrCodeStr: null,
    };
  },
  created() {
    let query = this.$route.query;
    let token = query.token;

    let pk = this.$route.query.rtePlanPk || "";
    // let pk = "4073263317116658";
    // let token = "dcp-sid-e5382083-3f59-4132-b511-f3708f9c8381";
    this.getInfo(pk, token);
    this.activeTitle.forEach((item) => {
      this.activeName.push(item.title);
    });
  },
  mounted() {},

  methods: {
    getInfo(pk, token) {
      let param = {
        id: pk,
        token: token,
      };
      $http
        .getrtePlanInfoGov(param)
        .then((res) => {
          if (res.code == 0) {
            this.dataList = res.data;
            // 一单四状态处理
            if (this.dataList.backTm) {
              this.dataList.transportationStatus = "结束";
              this.dataList.transportationStatusCode = 4;
              this.dataList.operateTm = this.dataList.backTm;
            } else if (this.dataList.unloadTm) {
              this.dataList.transportationStatus = "卸货";
              this.dataList.transportationStatusCode = 3;
              this.dataList.operateTm = this.dataList.unloadTm;
            } else if (this.dataList.loadTm) {
              this.dataList.transportationStatus = "装货";
              this.dataList.transportationStatusCode = 2;
              this.dataList.operateTm = this.dataList.loadTm;
            } else if (this.dataList.goTm) {
              this.dataList.transportationStatus = "发车";
              this.dataList.transportationStatusCode = 1;
              this.dataList.operateTm = this.dataList.goTm;
            } else {
              this.dataList.transportationStatus = "无";
              this.dataList.transportationStatusCode = 0;
              this.dataList.operateTm = "";
            }
            if (this.dataList.errBackStatus == 211) {
              this.dataList.transportationStatus = "异常结束";
              this.dataList.transportationStatusCode = -1;
              this.dataList.operateTm = this.dataList.errBackTm;
            }
            // 二维码
            this.createdQRCode(pk, token);
          } else {
            // this.$toast("载入电子运单数据出错！");
            if (res.msg) {
              alert(res.msg);
            }
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    createdQRCode(pk, token) {
      if (pk) {
        let param = {
          rtePlanPk: pk,
          token: token,
        };
        $http
          .createdQRCodeGov(param)
          .then((res) => {
            if (res) {
              new QRCode(document.getElementById("qrcode"), {
                text: res,
                width: 150,
                height: 150,
                colorDark: "#000000",
                colorLight: "#ffffff",
                correctLevel: QRCode.CorrectLevel.H,
              });
              this.qrCodeStr = res;
              this.createdBigQRCode();
            }
          })
          .catch((error) => console.log(error));
      }
    },
    createdBigQRCode() {
      new QRCode(document.getElementById("qrcodeBig"), {
        text: this.qrCodeStr,
        width: 350,
        height: 350,
        colorDark: "#000000",
        colorLight: "#ffffff",
        correctLevel: QRCode.CorrectLevel.H,
      });
    },
    showOverlay() {
      this.show = true;
      // this.createdBigQRCode();
    },
  },
};
</script>

<style lang="scss" scoped>
.main-content {
  width: 100vw;
  // height: 100vh;
  background-color: #eeeeee;
}
.container {
  padding-bottom: 20px;

  .timeline li {
    // background: url("@/images/back3.png") 0.5rem 0 repeat-y;
    background: url("~static/images/rtePlan/back3.png") 30px 0 repeat-y;
    height: 320px;
    position: relative;
    /* margin-bottom: 10px; */
    display: flex;
    overflow: hidden;
  }
  .fali {
    background: url("~static/images/rtePlan/back2.png") 0.5rem 0 repeat-y;
  }
  .check {
    flex: 0 0 80px;

    span {
      font-size: 16px;
      width: 36px;
      height: 36px;
      border-radius: 50%;
      position: absolute;
      display: block;
      left: 20px;
      margin-left: -10px;
      top: 0;
      line-height: 36px;
      text-align: center;
      border: 1px solid #c5c5c5;
      background: #c5c5c5;
      color: #fff;
    }
    .fatext {
      background: #45c473;
      border: 1px solid #45c473;
    }
  }
  .contentSecond {
    position: relative;
    height: 340px;
    flex: 1;
    border-left: 8px solid #41838e;
    background: #fff;

    b {
      position: absolute;
      display: block;
      width: 43px;
      height: 56px;
      background: url("~static/images/rtePlan/sprite.png") no-repeat;
      left: -30px;
      top: -2px;
    }
    p {
      margin: 5px 0 5px 5px;
      font-size: 0.4rem;
      line-height: 0.5rem;
    }
  }
  /* 高亮样式 */
  .timeline li.active {
    height: 340px;
  }

  .timeline .active .contentSecond {
    border-left: 5px solid #f26328;
  }

  .timeline .active .contentSecond b {
    background: url("~static/images/rtePlan/sprite.png");
    left: -15px;
  }
  .errorli,
  .huili {
    display: none;
  }
  .huili,
  .errorli {
    background: none;
  }
  .statusActive {
    color: rgb(37, 37, 238);
  }
}
.collapse {
  margin-bottom: 20px;
}
::v-deep .van-cell {
  color: #679de0 !important;
}
.collapse-one {
  margin-bottom: 20px !important;
}
.footer {
  padding: 0px 0 20px;

  .text-algin {
    margin: 0 auto;
    .footer-h4 {
      text-align: center;
      font-size: 18px;
      padding-bottom: 10px;
    }
    .footer-tips {
      text-align: center;
      font-size: 18px;
    }
  }
  .fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
  }

  .hidden {
    display: block !important;
    .code-big {
      position: fixed;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }
  }
}
</style>
