import cookies from "js-cookie";
import storage from "good-storage";
const TokenKey = "dcysweb-token-e40Y6a0d3K8Rd19"; // token
const tokenCookieExpires = 7; // 默认失效时间为7天
const UserInfoKey = "dcysweb-info-riqwenojdqjoU98FE"; // 用户信息 {} {...}

// 获取token
export function getToken() {
  // return cookies.get(TokenKey) || "";
  return window.sessionStorage.getItem(TokenKey) || "";
}
// 保存token
export function setToken(token) {
  // cookies.set(TokenKey, token, { expires: tokenCookieExpires });
  window.sessionStorage.setItem(TokenKey, token);
  return token;
}
// 删除token
export function removeToken() {
  // Cookies.remove(TokenKey, { path: "/", domain: location.hostname });
  // cookies.remove(TokenKey);
  window.sessionStorage.removeItem(TokenKey);
  return "";
}

// 获取用户信息
export function getUserCache() {
  return storage.get(UserInfoKey, {});
}
// 保存用户信息
export function setUserCache(userInfo) {
  storage.set(UserInfoKey, userInfo);
  return userInfo;
}
// 删除用户信息
export function removeUserCache() {
  storage.remove(UserInfoKey);
  return {};
}
