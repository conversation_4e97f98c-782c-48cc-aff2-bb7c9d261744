<!--
  ** date: 2023-8-14
  ** author: zhangxx
  ** desc: 电子运单详情页
 -->
<template>
  <div class="page-container">
    <van-cell-group v-if="rtePlanInfo"
                    class="page-content">
      <van-cell title="基础信息"
                class="cell_title"
                icon="qr"></van-cell>
      <van-cell title="运单号"
                :value="rtePlanInfo.cd ? rtePlanInfo.cd : ''"></van-cell>
      <van-cell title="人车罐信息"
                class="cell_title"
                icon="qr"></van-cell>
      <van-cell title="挂车"
                is-link>
        <template>
          <span class="custom-title"
                @click="toRouter('/pages/vecInfo.html',rtePlanInfo.tracPk)">{{rtePlanInfo.tracCd ? rtePlanInfo.tracCd : ''}}</span>
        </template></van-cell>
      <van-cell title="挂车"
                is-link>
        <template>
          <span class="custom-title"
                @click="toRouter('/pages/vecInfo.html',rtePlanInfo.traiPk)">{{rtePlanInfo.traiCd ? rtePlanInfo.traiCd : ''}}</span>
        </template></van-cell>
      <van-cell title="罐体编号"
                :value="rtePlanInfo.tankNo ? rtePlanInfo.tankNo : ''"></van-cell>
      <van-cell title="驾驶员"
                is-link>
        <template>
          <span class="custom-title"
                @click="toRouter('/pages/personInfo.html',rtePlanInfo.dvPk)">{{rtePlanInfo.dvNm ? rtePlanInfo.dvNm : ''}}</span>
        </template></van-cell>
      <van-cell title="驾驶员联系方式"
                :value="rtePlanInfo.dvMob ? rtePlanInfo.dvMob : ''"></van-cell>
      <van-cell title="押运员"
                is-link>
        <template>
          <span class="custom-title"
                @click="toRouter('/pages/personInfo.html',rtePlanInfo.scPk)">{{rtePlanInfo.scNm ? rtePlanInfo.scNm : ''}}</span>
        </template></van-cell>
      <van-cell title="押运员联系方式"
                :value="rtePlanInfo.scMob ? rtePlanInfo.scMob : ''"></van-cell>
      <van-cell title="托运人信息"
                :value="rtePlanInfo.latitude ? rtePlanInfo.latitude : ''"></van-cell>
      <van-cell title="托运人"
                :value="rtePlanInfo.consignorAddr ? rtePlanInfo.consignorAddr : ''"></van-cell>
      <van-cell title="托运人联系方式"
                :value="rtePlanInfo.consignorTel ? rtePlanInfo.consignorTel : ''"></van-cell>
      <van-cell title="装货人信息"
                class="cell_title"
                icon="qr"></van-cell>
      <van-cell title="装货人"
                :value="rtePlanInfo.csnorWhseAddr ? rtePlanInfo.csnorWhseAddr:''"></van-cell>
      <van-cell title="起运地"
                :value="rtePlanInfo.csnorWhseDist ? rtePlanInfo.csnorWhseDist : ''"></van-cell>
      <van-cell title="详细地址"
                :value="rtePlanInfo.csnorWhseLoc ? rtePlanInfo.csnorWhseLoc : ''"></van-cell>
      <van-cell title="所属园区"
                :value="rtePlanInfo.csnorPark ? rtePlanInfo.csnorPark : ''"></van-cell>
      <van-cell title="联系人"
                :value="rtePlanInfo.csnorWhseCt ? rtePlanInfo.csnorWhseCt : ''"></van-cell>
      <van-cell title="联系方式"
                :value="rtePlanInfo.csnorWhseTel ? rtePlanInfo.csnorWhseTel   :   ''"></van-cell>
      <van-cell title="收货人信息"
                class="cell_title"
                icon="qr"></van-cell>
      <van-cell title="收货人"
                :value="rtePlanInfo.csneeWhseAddr ? rtePlanInfo.csneeWhseAddr:''"></van-cell>
      <van-cell title="目的地"
                :value="rtePlanInfo.csneeWhseDist ? rtePlanInfo.csnorWhseDist : ''"></van-cell>
      <van-cell title="详细地址"
                :value="rtePlanInfo.csneeWhseLoc ? rtePlanInfo.csneeWhseLoc : ''"></van-cell>
      <van-cell title="所属园区"
                :value="rtePlanInfo.csneePark ? rtePlanInfo.csneePark : ''"></van-cell>
      <van-cell title="联系人"
                :value="rtePlanInfo.csneeWhseCt ? rtePlanInfo.csneeWhseCt : ''"></van-cell>
      <van-cell title="联系方式"
                :value="rtePlanInfo.csneeWhseTel ? rtePlanInfo.csneeWhseTel   :   ''"></van-cell>
      <van-cell title="货物信息"
                class="cell_title"
                icon="qr"></van-cell>
      <van-cell title="货物名称"
                :value="rtePlanInfo.goodsNm ? rtePlanInfo.goodsNm : ''"></van-cell>
      <van-cell title="货物数量（吨）"
                :value="rtePlanInfo.loadQty ? rtePlanInfo.loadQty : ''"></van-cell>
      <van-cell title="包装规格"
                :value="rtePlanInfo.packType ? rtePlanInfo.packType : ''"></van-cell>
      <van-cell title="调度信息"
                class="cell_title"
                icon="qr"></van-cell>
      <van-cell title="调度员"
                :value="rtePlanInfo.dispatcher ? rtePlanInfo.dispatcher : ''"></van-cell>
      <van-cell title="城市配送">
        <template>
          <van-checkbox v-model="rtePlanInfo.cityDeliveryCheckbox"
                        shape="square"
                        disabled
                        style="float: right"></van-checkbox>
        </template></van-cell>
      <van-cell title="调度日期"
                :value="rtePlanInfo.reqtTm ? rtePlanInfo.reqtTm : ''"></van-cell>
      <van-cell title="起运日期"
                :value="rtePlanInfo.vecDespTm ? rtePlanInfo.vecDespTm : ''"></van-cell>
      <van-cell title="计划开始时间"
                :value="rtePlanInfo.planStartTm ? rtePlanInfo.planStartTm : ''"></van-cell>
      <van-cell title="计划结束时间"
                :value="rtePlanInfo.planEndTm ? rtePlanInfo.planEndTm : ''"></van-cell>
      <van-cell title="备注"
                class="cell_title"
                icon="qr"></van-cell>
      <van-cell title="其他"
                :value="rtePlanInfo.longitude ? rtePlanInfo.longitude : ''"></van-cell>
      <van-cell title="提货单号"
                :value="rtePlanInfo.shipOrdCustCd ? rtePlanInfo.shipOrdCustCd : ''"></van-cell>
    </van-cell-group>
    <div ref="qrcode"
         class="qrcode"
         align="center"></div>
  </div>
</template>

<script>

import * as $http from "@/apis/rtePlan";
import QRCode from "qrcodejs2";
import { Notify, Toast } from 'vant';
export default {
  data () {
    return {
      rtePlanInfo: {},
      qrCode: null, //
    }
  },
  created () {
    $http.getDtlById(this.$route.query.pk).then(res => {
      this.rtePlanInfo = res.data
      if (res.data.cityDelivery === 1) {
        this.rtePlanInfo.cityDeliveryCheckbox = true
      } else {
        this.rtePlanInfo.cityDeliveryCheckbox = false
      }

    })

  },
  mounted () {
    this.$nextTick(() => {
      this.createdQRCode(this.$route.query.pk)
    })

  },
  methods: {
    toRouter (router, item) {
      this.$router.push({
        path: router,
        query: { //query是个配置项
          pk: item
        }
      })
    },
    createdQRCode (argmtPk) {
      const _this = this
      if (argmtPk) {
        $http.getRtePlanStr(argmtPk).then(res => {
          if (res) {
            new QRCode(_this.$refs.qrcode, {
              text: res,
              width: 150,
              height: 150,
              colorDark: "#000000",
              colorLight: "#ffffff",
              correctLevel: QRCode.CorrectLevel.L,
            });
          }
        })
      }
    }
  }
}

</script>

<style lang="scss" scoped>
.page-content {
  width: 100%;
  height: 100%;
  background: #b9b9b9;
}

.cell_title {
  color: #679de0;
  margin-top: 40px;
}
.qrcode {
  // width: 150px;
  // height: 150px;
  padding: 20px;
}
</style>