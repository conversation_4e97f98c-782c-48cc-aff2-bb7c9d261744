/**
 * 榜单上传菜单配置列表
 *
 * 注意：路由中auth:true 是说明当前页面是需要有权限才能查看的,一般游客用户是不能进入查看
 */
const routes = [
  {
    path: "/wb",
    name: "wb",
    component: () => import("@/views/wb/index.vue"),
    meta: { title: "榜单上传页面", auth: false },
  },
  {
    path: "/wb/success",
    name: "success",
    component: () => import("@/views/wb/success.vue"),
    meta: { title: "上传成功", auth: false },
  },
];

export default routes;
