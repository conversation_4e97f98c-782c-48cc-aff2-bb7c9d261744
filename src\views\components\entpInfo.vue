<!--
  ** date: 2023-08-07
  ** author: fanls
  ** desc: 企业详情
 -->
<template>
  <div class="weui_msg">
    <van-cell-group v-if="entpInfo">
      <van-cell
        title="基本信息"
        style="background-color: #f7f8fa; color: #c8c9cc"
      >
        <template>
          <span class="custom-title">
            <template v-if="entpInfo.basicHandleFlag == ''"
              ><span style="color: red">未提交</span></template
            >
            <template v-else-if="entpInfo.basicHandleFlag === '1'"
              ><span style="color: #67c23a">审核通过</span>
            </template>
            <template v-else-if="entpInfo.basicHandleFlag === '2'">
              <span style="color: red">审核未通过，原因：</span>
              <template v-if="entpInfo.basicHandleRemark">{{
                entpInfo.basicHandleRemark
              }}</template>
              <template v-else>无</template>
            </template>
            <template v-else-if="entpInfo.basicHandleFlag === '0'"
              ><span style="color: #ffce42">待受理</span></template
            >
          </span>
        </template>
      </van-cell>
      <van-cell title="统一社会信用编码" :value="entpInfo.uscCd"> </van-cell>
      <van-cell title="类型" :value="entpInfo.legalRepIdType"> </van-cell>
      <van-cell title="法人代表" :value="entpInfo.legalRepNm"> </van-cell>
      <van-cell
        title="注册资本(万)"
        :value="
          entpInfo.regCaptital + entpInfo.regCaptitalUnit
            ? entpInfo.regCaptital + entpInfo.regCaptitalUnit
            : 0 + entpInfo.regCaptitalUnit
        "
      >
      </van-cell>
      <van-cell title="成立日期" :value="entpInfo.establishDate"> </van-cell>
      <van-cell title="地址" :value="entpInfo.location"> </van-cell>
      <van-cell title="企业业务类型" :value="entpInfo.catNmCn"> </van-cell>
      <van-cell title="营业期限" :value="entpInfo.busiEndDate"> </van-cell>
      <van-cell title="登记机关" :value="entpInfo.regDept"> </van-cell>
      <van-cell title="经营状态" :value="entpInfo.regStat"> </van-cell>
      <van-cell title="紧急联系人" :value="entpInfo.erNm"> </van-cell>
      <van-cell title="紧急联系人电话" :value="entpInfo.erMob"> </van-cell>
      <van-cell
        title="证件信息"
        style="background-color: #f7f8fa; color: #c8c9cc"
      >
      </van-cell>
      <div v-for="(item, index) in entpItems" :key="index" class="custom">
        <van-cell :title="item.licCatNmCn">
          <template>
            <span class="custom-title">
              <template v-if="item.handleFlag == ''"
                ><span style="color: red">未提交</span></template
              >
              <template v-else-if="item.handleFlag === '1'"
                ><span style="color: #67c23a">审核通过</span>
              </template>
              <template v-else-if="item.handleFlag === '2'">
                <span style="color: red">审核未通过，原因：</span>
                <template v-if="item.handleRemark">{{
                  item.handleRemark
                }}</template>
                <template v-else>无</template>
              </template>
              <template v-else-if="item.handleFlag === '0'"
                ><span style="color: #ffce42">待受理</span></template
              >
            </span>
          </template>
        </van-cell>
        <div class="img_box">
          <div class="id_title" v-if="item.licVldTo">
            有效期：{{ item.licVldTo }}
          </div>
          <div class="id_box">
            <div
              class="id_front"
              v-for="(el, elIndex) in item.subItems"
              :key="elIndex"
              @click="onImgPreView(el.thumbnailUrl)"
            >
              <div class="img_text">{{ item.licCatNmCn }}</div>
              <div class="img">
                <van-image
                  v-if="el.thumbnailUrl"
                  width="100%"
                  height="100%"
                  :src="el.thumbnailUrl"
                />
              </div>
            </div>
          </div>
        </div>
        <div v-if="index + 1 < entpItems.length" class="custom-else"></div>
      </div>
    </van-cell-group>
  </div>
</template>

<script>
import * as $http from "@/apis/vec_pers_entp_tank.js";
import { ImagePreview } from "vant";

export default {
  data() {
    return {
      entpInfo: {},
      entpItems: [],
    };
  },
  created() {
    let pk = this.$route.query.pk || "";
    // let pk = "4058484883182597";
    this.init(pk);
  },
  methods: {
    init(pk) {
      $http.getEntpInfo(pk).then((res) => {
        if (res.code === 0) {
          this.entpInfo = res.data.entp;
          this.entpItems = res.data.items;
        }
      });
    },
    // 预览图片
    onImgPreView(url) {
      ImagePreview({
        images: [url],
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.weui_msg {
  width: 100vw;
  height: 100vh;
}

.custom {
  // margin-bottom: 30px;
}
.custom-else {
  width: 100vw;
  height: 30px;
  background: rgb(233, 233, 233);
}

.img_box {
  width: 90%;
  height: 252px;
  padding: 15px;
}
.id_title {
  color: #f8121c;
  font-size: 28px;
}
.id_box {
  width: 100%;
  height: 100%;
  display: flex;
  overflow-x: auto;
}
.id_front {
  margin-top: 15px;
  min-width: 29.5%;
  width: 29.5%;
  margin-right: 3%;
  height: 75%;
}
.img {
  width: 100%;
  height: 80%;
  background: url("~static/images/emergency/camera.png") no-repeat center center;
  background-size: 100% 100%;
}
.img_text {
  margin-bottom: 10px;
  font-size: 18px;
}
</style>
