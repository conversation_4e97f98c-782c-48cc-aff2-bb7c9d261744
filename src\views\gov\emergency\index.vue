<!--
  ** date: 2023-7-20
  ** author: zhangxx
  ** desc: 应急救援
 -->
<template>
  <div class="weui_msg">
    <div class="vec">
      <div class="vec_prompt"
           v-if="cd"
           @click="showPopup">车辆未完结电子运单信息</div>
    </div>
    <van-cell-group v-if="sosInfo">
      <van-cell title="牵引车"
                is-link>
        <template>
          <span class="custom-title"
                @click="toRouter('/pages/vecInfo.html',sosInfo.tracPk)">{{sosInfo.tracCd}}</span>
        </template>
      </van-cell>
      <van-cell title="挂车"
                is-link>
        <template>
          <span class="custom-title"
                @click="toRouter('/pages/vecInfo.html',sosInfo.traiPk)">{{sosInfo.traiCd}}</span>
        </template></van-cell>
      <van-cell title="运输企业"
                is-link>
        <template>
          <span class="custom-title"
                is-link
                @click="toRouter('/pages/entpInfo.html',sosInfo.carrierPk)">{{sosInfo.carrierNm}}</span>
        </template></van-cell>
      <van-cell title="货物"
                is-link
                @click="toRouter('/pages/goodsInfo.html',sosInfo.prodPk)"><template>
          <span class="custom-title">{{sosInfo.goodsNm}}</span>
        </template></van-cell>
      <van-cell title="货物重量"><template>
          <span class="custom-title">{{sosInfo.loadQty?sosInfo.loadQty+'吨':''}}</span>
        </template></van-cell>
      <van-cell title="司机"
                is-link
                @click="toRouter('/pages/personInfo.html',sosInfo.dvPk)"><template>
          <span class="custom-title">{{sosInfo.dvNm}}</span>
        </template></van-cell>
      <van-cell title="押运员"
                is-link
                @click="toRouter('/pages/personInfo.html',sosInfo.scPk)"><template>
          <span class="custom-title">{{sosInfo.scNm}}</span>
        </template></van-cell>
      <van-cell title="发货方"
                :value="sosInfo.consignorAddr"></van-cell>
      <van-cell title="收货方"
                :value="sosInfo.csneeWhseAddr"></van-cell>
      <van-cell title="发运日期"
                :value="sosInfo.crtTm"></van-cell>
      <van-cell title="卫星定位最近定位时间"
                :value="updateTime"></van-cell>
      <van-cell title="24小时历史轨迹"
                is-link>
        <template>
          <span class="custom-title"
                @click="getHistory(sosInfo.tracCd)">查看</span>
        </template></van-cell>
    </van-cell-group>
    <van-picker v-if="showPicker"
                show-toolbar
                :columns="columns"
                @confirm="onConfirm"
                @cancel="onCancel" />
    <van-popup v-model="show">
      <div class="rteplan_title">车辆未完结电子运单信息</div>
      <van-cell-group v-if="sosInfo">
        <van-cell title="运单编号"
                  is-link>
          <template>
            <span class="custom-title">{{sosInfo.cd}}</span>
          </template>
        </van-cell>
        <van-cell title="牵引车"
                  is-link>
          <template>
            <span class="custom-title">{{sosInfo.tracCd}}</span>
          </template>
        </van-cell>
        <van-cell title="挂车"
                  is-link>
          <template>
            <span class="custom-title">{{sosInfo.traiCd}}</span>
          </template></van-cell>
        <van-cell title="货物"
                  is-link><template>
            <span class="custom-title">{{sosInfo.goodsNm}}</span>
          </template></van-cell>
        <van-cell title="货物重量"><template>
            <span class="custom-title">{{sosInfo.loadQty}}吨</span>
          </template></van-cell>
        <van-cell title="驾驶员"
                  is-link><template>
            <span class="custom-title">{{sosInfo.dvNm}}</span>
          </template></van-cell>
        <van-cell title="押运员"
                  is-link><template>
            <span class="custom-title">{{sosInfo.scNm}}</span>
          </template></van-cell>
      </van-cell-group>
    </van-popup>
  </div>
</template>

<script>
import * as $http from "@/apis/sos";
import * as $httpGoods from "@/apis/vec_pers_entp_tank";
import { Notify, Toast } from 'vant';
export default {
  data () {
    return {
      show: false,
      plateNo: '',
      isMarket: false,
      showPicker: false,//选择器控制
      economize: "浙",
      market: "B",
      columns: [],
      sosInfo: null,
      cd: null,
      updateTime: null,
    }
  },
  created () {

  },
  mounted () {
    this.getLast4Wechat(this.$route.query.tracCd)
    // this.getLast4Wechat()
  },
  methods: {

    getLast4Wechat (tracCd) {
      $http.getLast4Wechat(tracCd).then(res => {
        if (res.code === 0 && res.data) {
          this.sosInfo = res.data
          this.cd = this.sosInfo.cd
        }
      })
      $http.findGpsByVecNo(tracCd).then(res => {
        if (res.code === 0 && res.data) {
          this.updateTime = res.data.updateTime
        }
      })

    },
    showPopup () {
      this.show = true;
    },
    //查看24小时历史轨迹
    getHistory (tracCd) {
      window.open("https://zhys-ppt.dacyun.com/map.v.1.1/mobile-trace/?v=" + tracCd);
    },
    toRouter (router, item) {
      this.$router.push({
        path: router,
        query: { //query是个配置项
          pk: item
        }
      })
    },
    //选择器取消
    onCancel () {
      this.showPicker = false
    },
    // 获取A-Z的数组
    generateArrayUpper () {
      let arrGenerate = []
      for (var i = 0; i < 26; i++) {
        var letter = String.fromCharCode(65 + i);
        arrGenerate.push(letter);
      }
      return arrGenerate
    },
  }
}

</script>

<style lang="scss" scoped>
.weui_msg {
  width: 100vw;
  height: 100vh;
}
.vec {
  padding: 15px 0 10px 15px;
  background: #e0e0e0;
  .vec_text {
    width: 60px;
    height: 80px;
    background: #2669c2;
    color: #fff;
    margin: 2px 5px 0 5px;
    line-height: 80px;
    display: inline-block;
    text-align: center;
  }
  .van-cell {
    // background: #e0e0e0;
    width: 60%;
  }
}
.vec_prompt {
  margin-top: 10px;
  text-align: left;
  background: #e0e0e0;
}
.van-cell {
  // background: #e0e0e0;
  // width: 100%;
  text-align: left;
}
.van-picker {
  position: absolute;
  bottom: 0;
  width: 100%;
}
.van-popup--center {
  width: 95%;
  height: 70%;
  border-radius: 6px;
}
.rteplan_title {
  width: 100%;
  background: #0089ff;
  color: #ffffff;
  line-height: 80px;
  text-align: center;
}
</style>