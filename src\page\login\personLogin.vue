<!--
  ** date: 2023-7-20
  ** desc: 驾押人员账号登录
 -->
<template>
  <div class="login-page">
    <div class="login-content">
      <div class="login-title-wrapper">
        <div class="login-subtitle">{{ subtitle }}</div>
      </div>
      <div class="login-form">
        <van-form @submit="onSubmit"
                  ref="editForm"
                  class="edit-form">
          <van-field v-model="editForm.xm"
                     label=""
                     placeholder="请输入姓名"
                     :rules="$formRules({ required: true })">
            <template #left-icon>
              <van-icon class="iconfont"
                        class-prefix='icon'
                        name='wode' />
            </template>
          </van-field>
          <van-field v-model="editForm.sfz"
                     label=""
                     placeholder="请输入身份证号"
                     :rules="$formRules({ required: true, type: 'ID' })">
            <template #left-icon>
              <van-icon class="iconfont"
                        class-prefix='icon'
                        name='jibenxinxi' />
            </template>
          </van-field>
          <van-field v-model="editForm.mobile"
                     type="tel"
                     label=""
                     placeholder="请输入手机号"
                     :rules="$formRules({ required: true, type: 'mobile' })">
            <template #left-icon>
              <van-icon class="iconfont"
                        class-prefix='icon'
                        name='shoujihao' />
            </template>
          </van-field>
          <van-field v-model="editForm.vcode"
                     type="number"
                     center
                     clearable
                     label=""
                     placeholder="请输入手机验证码"
                     use-button-slot
                     :rules="$formRules({ required: true })">
            <template #left-icon>
              <van-icon class="iconfont"
                        class-prefix='icon'
                        name='yanzhengma' />
            </template>
            <van-button :disabled="isClick || !isValidMobile"
                        slot="button"
                        size="small"
                        type="info"
                        native-type="button"
                        @click="sendPhoneCode">{{ codeText
              }}</van-button>
          </van-field>
          <div style="margin: 16px">
            <van-button round
                        block
                        type="info"
                        native-type="submit">提交</van-button>
          </div>
        </van-form>
      </div>
    </div>
  </div>
</template>

<script>
import { Toast, Notify } from "vant";
import {
  sendMobCode,
  bindPersonLogin,
} from "@/apis/user";
import { isMobile, isID } from "@/utils/validate";

export default {
  name: "entpLogin",
  data () {
    return {
      title: process.env.VUE_APP_TITLE,

      subtitle: "驾押人员登录",
      codeText: "获取验证码",
      isClick: false,  // 是否已点击发送验证码

      editForm: {
        xm: "",
        sfz: "",
        mobile: "",
        vcode: "",
      },
    };
  },
  computed: {
    isValidMobile () {
      let val = this.editForm.mobile
      return val && isMobile(val)
    }
  },
  mounted () {
  },
  methods: {
    phoneValidator (val) {
      return isMobile(val)
    },
    // 获取验证码
    async sendPhoneCode () {
      let mobile = this.editForm.mobile;
      let self = this;
      if (!this.isClick) {
        if (isMobile(mobile)) {
          this.isClick = true;
          const res = await sendMobCode(mobile, 3).catch(e => { console.log(e); self.isClick = false }); // 获取验证码接口
          let s = 60;
          this.codeText = s + "s";
          let interval = setInterval(() => {
            s--;
            this.codeText = s + "s";
            if (s < 0) {
              this.codeText = "重新获取";
              this.isClick = false;
              clearInterval(interval);
            }
          }, 1000);
          if (res.code !== 0) {
            this.isClick = false;
          }
        } else {
          this.isClick = false;
          Toast.fail("请输入正确的手机号码");
        }
      }
    },
    onSubmit () {
      let self = this;
      this.$refs.editForm
        .validate()
        .then(() => {
          let postData = self.editForm
          bindPersonLogin(postData).then((bRes) => {
            if (
              bRes.code === 0
            ) {
              Toast(bRes.msg);
              self.$store.dispatch("user/setToken", bRes.token);

              let query = self.$route.query;
              let redirectUrl = query.redirect || "/";
              self.$router.push({
                path: redirectUrl,
              });
            } else {
              Notify({ type: "danger", message: bRes.msg });
            }
          }).catch(() => {
            Toast("绑定失败，请联系管理员！");
          });
        })
        .catch(() => {
          Toast("用户信息填写不规范");
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.login-page {
  position: relative;
  width: 100%;
  height: 100%;
  background: url('~static/images/home/<USER>') no-repeat 0 0;
  background-size: 100% auto;

  .login-content {
    position: absolute;
    width: 690px;
    height: 638px;
    top: 292px;
    left: 50%;
    transform: translateX(-50%);
    background: #fff;
    box-shadow: 0px 1px 20px 0px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    padding-top: 60px;

    .login-title-wrapper {
      font-weight: 500;
      color: #0f50e6;
      text-align: center;

      .login-title {
        font-size: 30px;
        line-height: 58px;

        &::after {
          width: 514px;
          height: 40px;
          background: #2361e3;
          opacity: 0.15;
        }
      }

      .login-subtitle {
        font-size: 42px;
        line-height: 58px;
      }
    }

    .login-form {
      margin: 50px;

      .edit-form {
      }
    }
  }
}

.iconfont {
  font-size: 44px;
}
</style>
