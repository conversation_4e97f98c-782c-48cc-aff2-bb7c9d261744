<!--
  @desc:附近车辆
  @date:2023-08-15
-->
<!-- 地址/nearbyvec.html?token=?&lng=?&lat=?&radius=? -->
<template>
  <div class="page">
    <div class="toolbar">
      <div class="slider-wrap">
        <div class="scope">范围(米)：</div>
        <van-slider v-model="radius" @change="onstatechange" :min="0" :max="10000"></van-slider>
        <div class="meter">{{radius}}米</div>
      </div>
      <van-row class="switch-wrap-two">
        <van-col span="12" class="switch-wrap longString" >
          全部车辆<van-switch v-model="checked" size="24" @change="switchVecBtnHandle"/> 报警车辆
        </van-col>
        <van-col span="12" class="switch-wrap">
          关闭选点 <van-switch v-model="checked1" size="24" @change="switchMapPointBtnHandle"/> 开启选点
        </van-col>
      </van-row>
    </div>
    <div class="map-container">
      <div id="map"></div>
      <img v-show="showCoords" class="map-center" src="/images/nearbyvec/coords.png" id="dragCenterMarker"/>
    </div>
    <img @click="getLocation" class="locationImg" src="images/nearbyvec/location1.png"/>
  </div>
</template>

<script>
import * as $http from "@/apis/nearbyvec.js"
var areaRelationship = {
  330211: "/zh", // 镇海
  330604: "/sy", // 上虞
  // "330206007":"/zh", // 北仑青峙
};
var token = getUrlParms("token");
var areaId = getUrlParms("areaId");
var apiPrefix = "/zh";
// 上虞区域的则api转成上虞的
if (areaId && areaRelationship[areaId]) {
  apiPrefix = areaRelationship[areaId];
}
var lng = getUrlParms("lng");
var lat = getUrlParms("lat");
var isr = false;
var timer = null;
var isDrag = false;
var timerDrag = null;
var map, pointPoistion, locationMarker;
//var checked = true; //是否显示报警车
//var checked1 = true; //是否开始地图选点
var coverPoint = null; //搜索定位
function getUrlParms(name) {
  var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
  var r = window.location.search.substr(1).match(reg);
  if (r != null) return decodeURI(r[2]);
  return null;
}
export default {
  name: "nearby",
  data() {
    return {
      radius: 2000,
      checked:true,//是否显示报警车
      checked1:true,//是否开始地图选点
      showCoords:false
    };
  },
  created() {
    let query = this.$route.query
    let token = query.token
    this.$store.dispatch("user/setToken", token);
  },
  mounted() {
    this.initMap();
  },
  methods: {
    //地图初始化
    initMap() {
      if (!map) {
        map = new BMap.Map("map");
        pointPoistion = new BMap.Point(lng, lat);
        locationMarker = new BMap.Marker(pointPoistion, {
          icon: new BMap.Icon("/images/nearbyvec/coords.png", new BMap.Size(38, 38)),
          enableMassClear: false,
        });
        map.addOverlay(locationMarker);
        map.centerAndZoom(pointPoistion, 10); //对地图进行初始化
        map.enableScrollWheelZoom(); //启用滚轮放大缩小
      }
      this.getLocation(); //获取当前定位
      this.openMapCheckPointEventListener(this.checked1)
    },
    //获取当前定位
    getLocation() {
      map.centerAndZoom(pointPoistion, 13);
      this.cover(pointPoistion, this.checked);
    },
    //搜索区域
    cover(point, isAlarm) {
      const _this = this;
      // console.log(map.getCenter());
      coverPoint = point;
      map.clearOverlays();
      this.radius = this.radius ? this.radius : 2000;
      locationMarker.setPosition(point);
      map.panTo(point);
      var circle = new BMap.Circle(point, this.radius);
      map.addOverlay(circle);
      if (isDrag) {
        clearTimeout(timerDrag);
      } else {
        isDrag = true;
      }
      timerDrag =setTimeout(async function () {
        let data = {
          lng: point.lng,
          lat: point.lat,
          radius: _this.radius,
        }
        let ajaxName = 'findNearByGpsZH'
        if (apiPrefix === '/sy'){
          ajaxName = 'findNearByGpsSY'
        }
        const res = await $http[ajaxName](data)
        if (res.code == 0 && res.data.length > 0) {
          res.data.forEach((item, i) => {
            if (isAlarm) {
              //只显示报警车
              if (item.isalarm == 1) {
                _this.coverData(item);
              }
            } else {
              _this.coverData(item);
            }
          });
        } else {
          if (res.msg) {
            alert(res.msg);
          }
        }

      }, 500);
    },
    //
    coverData(item) {
      const _this = this;
      let runStatus, vehicleNo, updateTime, icon;
      let point = new BMap.Point(item.lonBd, item.latBd);
      vehicleNo = item.vehicleNo || item._id;
      runStatus = item.speed > 0 ? "run" : "stop";
      updateTime = new Date().getTime() - item.updateTimeStamp;
      if (item.state == 0 || updateTime > 10 * 1000 * 60) {
        // 离线
        runStatus = "offline";
      }
      icon = new BMap.Icon(
          "/images/monitor-img/" + runStatus + "_" + item.carType + ".png",
          new BMap.Size(24, 24)
      );
      let marker = new BMap.Marker(point, {
        icon: icon,
      });
      marker.setRotation(item.direction);
      map.addOverlay(marker);

      var distance = this.getDistance(
          item.latBd,
          item.lonBd,
          coverPoint.lat,
          coverPoint.lng
      );
      marker.addEventListener("click", function () {
        _this.getLast(this, item, distance);
        _this.switchMapPointBtnHandle(false);
      });

    },
    //
    getDistance(lat1, lng1, lat2, lng2) {
      var radLat1 = (lat1 * Math.PI) / 180.0;
      var radLat2 = (lat2 * Math.PI) / 180.0;
      var a = radLat1 - radLat2;
      var b = (lng1 * Math.PI) / 180.0 - (lng2 * Math.PI) / 180.0;
      var s =
          2 *
          Math.asin(
              Math.sqrt(
                  Math.pow(Math.sin(a / 2), 2) +
                  Math.cos(radLat1) *
                  Math.cos(radLat2) *
                  Math.pow(Math.sin(b / 2), 2)
              )
          );
      s = s * 6378.137; // EARTH_RADIUS;
      s = Math.round(s * 10000) / 10000;
      return s; // 单位为km
    },
    //
    async getLast(marker, item, distance) {
      //获取上一单电子运单
      console.log(item);
      let vehicleNo = item.vehicleNo || item._id;
      var day = new Date();
      var today =
          day.getFullYear() + "-" + (day.getMonth() + 1) + "-" + day.getDate();
      let startTime = today + " 00:00:00";
      let endTime = today + " 23:59:59";

      var sContent =
          "<h4 style='margin:0 0 5px 0;padding:0.2em 0'>" +
          vehicleNo +
          "</h4>" +
          "<div class='info'><div><span>企业名称：</span>" +
          item.entpName +
          "</div>" +
          "<div><span>卫星定位时间：</span>" +
          item.updateTime +
          "</div>" +
          "<div ><div ><span>速度：</span><span style='color:black;'>" +
          item.speed +
          "千米/小时</span></div><div ><span>距离：</span><span style='color:black;'>" +
          (distance * 1000).toFixed(2) +
          "米</span></div></div>";
      let ajaxName = 'getCurrentRtePlanZH'
      if (apiPrefix === '/sy'){
        ajaxName = 'getCurrentRtePlanSY'
      }
      const res = await $http[ajaxName](vehicleNo)
      if (res.code == 0 && res.data) {
        let data = res.data;
        sContent +=
            "<div ><div ><span>牵引车：</span><span style='color:black;'>" +
            (item.vehicleNo || item._id || "") +
            "</span></div><div ><span>挂车：</span><span style='color:black;'>" +
            (data.traiCd || "") +
            "</span></div></div>" +
            "<div><span>驾驶员：</span>" +
            data.dvNm +
            "/" +
            data.dvMob +
            "</div>" +
            "<div><span>押运员：</span>" +
            data.scNm +
            "/" +
            data.scMob +
            "</div>" +
            "<div style='color:#0087d6;'><span>运单号：</span>" +
            "<span><a href='./rteplanInfoGov.html?argmtPk=" +
            data.argmtPk +
            "&token=" +
            token +
            "'>" +
            data.cd +
            "</a></span></div>";
        if (data.invalid == 1) {
          sContent +=
              "<div><span>运单状态：无效</span></div>" +
              "<div><span>运单无效原因：</span>" +
              data.invalidReason +
              "</div>";
        } else {
          sContent += "<div><span>运单状态：</span>有效</div>";
        }
        sContent +=
            "<div><span>货物：</span>" +
            data.goodsNm +
            "</div>" +
            "<div><span>发货地：</span><span style='color:black;'>" +
            data.csnorWhseDist +
            "（" +
            data.csnorWhseAddr +
            "）</span></div>" +
            "<div><span>收货地：</span><span style='color:black;'>" +
            data.csneeWhseDist +
            "（" +
            data.csneeWhseAddr +
            "）</span></div>";
      } else {
        sContent +=
            "<div><span>运单号：<span style='color:red'>暂无电子运单</span></span></div>";
      }
      if (this.checked) {
        let ajaxName1 = 'getAlarmTodayByVecNoZH'
        if (apiPrefix === '/sy'){
          ajaxName1 = 'getAlarmTodayByVecNoSY'
        }
        const response = await $http[ajaxName1](vehicleNo)
        if (
            response.code == 0 &&
            response.data &&
            response.data.length > 0
        ) {
          let alarmData = response.data[0];
          sContent +=
              "<div><span>报警类型：</span>" +
              alarmData.catNmCn +
              "</div>" +
              "<div><span>报警地点：</span>" +
              alarmData.alarmLocation +
              "</div>" +
              "<div><span>报警时间：</span>" +
              alarmData.alarmTime +
              "</div>" +
              "<div><span>报警详情：</span>" +
              alarmData.descr +
              "</div>" +
              "<a style='margin-top:10px;' href='./mobile-trace.html?v=" +
              vehicleNo +
              "&startTime=" +
              startTime +
              "&endTime=" +
              endTime +
              "&token=" +
              token +
              "'>历史轨迹</a>" +
              "<a style='margin-top:10px;margin-left:10px;' href='./violation-record.html?v=" +
              vehicleNo +
              "&token=" +
              token +
              "'>违章记录</a>" +
              "<a style='margin-top:10px;margin-left:10px;' href='./tracking.html?vecNo=" +
              vehicleNo +
              "&token=" +
              token +
              "'>车辆追踪</a>";
        } else {
          sContent +=
              "<a style='margin-top:10px;margin-left:10px;' href='./violation-record.html?v=" +
              vehicleNo +
              "&token=" +
              token +
              "'>违章记录</a>" +
              "<a style='margin-top:10px;margin-left:10px;' href='./tracking.html?vecNo=" +
              vehicleNo +
              "&token=" +
              token +
              "'>车辆追踪</a>";
        }
        sContent += "</div>";
        var infoWindow = new BMap.InfoWindow(sContent); // 创建信息窗口对象
        marker.openInfoWindow(infoWindow);
      } else {
        sContent +=
            "<a style='margin-top:10px;margin-left:10px;' href='./violation-record.html?v=" +
            vehicleNo +
            "&token=" +
            token +
            "'>违章记录</a>";
        sContent += "</div>";
        var infoWindow = new BMap.InfoWindow(sContent); // 创建信息窗口对象
        marker.openInfoWindow(infoWindow);
      }
    },

    //报警车辆
    switchVecBtnHandle(e) {
      this.checked = e
      this.cover(coverPoint, this.checked);
    },
    //地图选点
    switchMapPointBtnHandle(isopen) {
      if (isopen !== undefined) {
        this.checked1 = false;
      }
      this.checked1 = isopen
      this.openMapCheckPointEventListener(this.checked1);
    },
    // 拖动地图开始
    dragstartEvent() {
      console.log('start');
      map.removeOverlay(locationMarker);
      this.showCoords = true
    },
    // 拖动地图结束
    dragendEvent() {
      console.log('end');
      const _this = this;
      this.showCoords = false
      setTimeout(function () {
        var center = map.getCenter();
        locationMarker.setPosition(center);
        map.addOverlay(locationMarker);
        _this.cover(center, _this.checked);
      }, 10);
    },
    openMapCheckPointEventListener(isopen) {
      const _this = this;
      console.log("开启监听。。。。");
      if (!map) return;
      if (isopen) {
        map.addEventListener("dragstart", _this.dragstartEvent);
        map.addEventListener("dragend", _this.dragendEvent);
        map.addEventListener("zoomend", _this.dragendEvent);
        // map.addEventListener('longpress', _this.dragendEvent);
      } else {
        _this.dragstartEvent && map.removeEventListener("dragstart", _this.dragstartEvent);
        _this.dragendEvent && map.removeEventListener("dragend", _this.dragendEvent);
        _this.dragendEvent && map.removeEventListener("zoomend", _this.dragendEvent);
        // dragendEvent && map.removeEventListener("longpress", _this.dragendEvent)
      }
    },
    //范围变化
    onstatechange() {
      const _this = this;
      //数字变化的时候的回调函数
      if (isr) {
        clearTimeout(timer);
      } else {
        isr = true;
      }
      timer = setTimeout(function () {
        _this.cover(map.getCenter(), _this.checked);
      }, 500);
    },
  },
};
</script>

<style scoped>
.page,
.map {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  margin: 0;
  font-family: "微软雅黑";
}
.toolbar{
  height: 100px;
}
.map-container {
  position: relative;
  height: calc(100% - 100px);
  margin-top: 100px;
}
#map {
  width: 100%;
  height: 100%;
  overflow: hidden;
  margin: 0;
  font-family: "微软雅黑";
}
.slider-wrap{
  display: flex;
  border-bottom: 1px dashed #ccc;
  padding: 30px 2%;
  justify-content: center;
  align-items: center;
}
.scope{
  width: 200px;
  text-align: left;
  white-space: nowrap;
}
.meter{
  width: 200px;
  text-align: right;
  white-space: nowrap;
}
.switch-wrap-two{
  padding: 20px 10px;
}
.switch-wrap{
  display: flex;
  align-items: center;
  justify-content: space-evenly;
}
.longString{
  border-right: 1px dashed #888787;
}
.locationImg {
  width: 100px;
  height: 100px;
  position: fixed;
  bottom: 20px;
  right: 20px;
}
.map-center {
  position: absolute;
  width: 80px;
  height: 80px;
  top: 50%;
  left: 50%;
  transform: translateX(-50%) translateY(-50%);
}
</style>