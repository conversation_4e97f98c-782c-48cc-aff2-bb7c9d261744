<!--
  * @date: 2023-8-11
  * @desc:司机上报举证内容（下发举证）
-->
<template>
  <div class="main-content">
    <div class="full-screen" v-if="loading">
      <van-loading type="spinner" color="#1989fa" class="loading" />
    </div>
    <van-cell-group title="报警详情">
      <van-cell title="报警编号：" :value="dataForm.cd" title-width="10px"></van-cell>
      <van-cell title="车牌：" :value="dataForm.tractorNo"></van-cell>
      <van-cell title="驾驶员：" :value="dataForm.driverNm"></van-cell>
      <van-cell title="报警详情：" :value="dataForm.descr"></van-cell>
    </van-cell-group>
    <van-cell-group title="举证内容" v-if="dataForm && dataForm.isReply">
      <!-- isReply:1 下发举证，isReply：2 已回函，isReply：其他未下发举证 -->
      <!-- 已下发举证 -->
      <template v-if="dataForm.isReply === 1">
        <van-field v-model="dataForm.replyRemark" label="情况说明：" type="textarea" placeholder="请输入情况说明" autosize
          :rules="[{ required: true, message: '请输入情况说明' }]"></van-field>
        <van-cell>
          <template #title>
            <span class="custom-title">上传附件：<sup style="color: red">*</sup></span>
          </template>
        </van-cell>
        <div style="margin:0.25rem 0;text-align:center;">
          <van-tag type="danger">请根据实际情况上传照片，道路拥堵照片，车辆维修照片等。</van-tag>
        </div>
        <van-field name="uploader" label="" style="padding: 0 10px">
          <template #input>
            <van-uploader v-model="dataForm.replyUrlArr" :after-read="afterRead" :max-count="5">
            </van-uploader>
          </template>
        </van-field>
        <div style="margin:0 0.25rem;">
          <van-button class="btn" round type="info" @click="submit" style="width:100%;">提交</van-button>
        </div>
      </template>
      <!-- 已回函 -->
      <template v-else-if="dataForm.isReply === 2">
        <van-cell title="情况说明：" :value="dataForm.replyRemark"></van-cell>
        <van-cell :value="(dataForm && dataForm.replyUrlArr && dataForm.replyUrlArr.length) ? '' : '无'">
          <template #title>
            <span class="custom-title">上传的附件：</span>
            <!-- <van-tag type="danger">请根据实际情况上传照片，道路拥堵照片，车辆维修照片等。</van-tag> -->
          </template>
        </van-cell>
        <van-cell v-if="dataForm && dataForm.replyUrlArr && dataForm.replyUrlArr.length">
          <van-row>
            <van-col span="8" v-for="(item, index) in dataForm.replyUrlArr" :key="index">
              <van-image width="100" height="100" lazy-load :src="item.url" fit="cover" @click="showPreview(item.url)" />
            </van-col>
          </van-row>
        </van-cell>
      </template>
    </van-cell-group>
    <van-dialog v-model="codeShow" title="短信验证码" :show-cancel-button="false" :before-close="onBeforeClose"
      confirm-button-text="确认" @confirm="codeSubmit">
      <van-cell-group>
        <div style="text-align: center; letter-spacing: 5px">
          {{ formatDvName }}
        </div>
        <van-password-input :value="dvMob" :mask="false" :length="11" :focused="showKeyboard"
          @focus="showKeyboard = true"></van-password-input>
        <van-field v-model="smsCode" type="digit" center clearable label="" placeholder="请输入短信验证码" :border="false"
          use-button-slot>
          <van-button slot="button" size="small" type="primary" @click="sendCode" v-show="canSent == true"
            :disabled="sentDisable">
            发送验证码
          </van-button>
          <van-button slot="button" size="small" type="primary" :disabled="true" v-show="canSent == false">
            {{ time }}秒后发送
          </van-button>
        </van-field>
      </van-cell-group>
    </van-dialog>
    <van-number-keyboard v-model="dvMob" :show="showKeyboard" @blur="showKeyboard = false"></van-number-keyboard>
  </div>
</template>

<script>
import { Notify, Toast, Dialog, Lazyload, ImagePreview } from 'vant';
import * as $http from "@/apis/alarm.js";
import * as $httpCommon from "@/apis/common.js"
import Vue from 'vue';
Vue.use(Lazyload);
export default {
  data() {
    return {
      codeShow: false,
      time: 60,
      dvMob: "", //手机号
      smsCode: "",
      canSent: true,
      sentDisable: true,
      showKeyboard: false,
      btnDisabled: false,
      dataForm: {
        cd: "",
        tractorNo: "",
        driverNm: "",
        descr: "",
        replyRemark: "",
        replyUrl: "",
        replyUrlArr: [],
      },
      isVerified: false,
      loading: false,
    }
  },
  watch: {
    dvMob(newValue, oldValue) {
      if (newValue === this.dataForm.dvMob) {
        this.sentDisable = false;
      } else {
        this.sentDisable = true;
      }
    },
  },
  created() {
  },
  computed: {
    //驾驶员姓名脱敏
    formatDvName() {
      if (this.dataForm.driverNm) {
        return this.formatName(this.dataForm.driverNm);
      } else {
        return "";
      }
    },
  },
  mounted() {
    // 非微信内部打开
    let query = this.$route.query;
    let cd = query?.cd || null;
    this.getInfo(cd);
  },
  methods: {
    //姓名脱敏
    formatName(name) {
      let newStr;
      if (name.length === 2) {
        newStr = name.substr(0, 1) + "*";
      } else if (name.length > 2) {
        let char = "";
        for (var i = 0, len = name.length - 2; i < len; i++) {
          char += "*";
        }
        newStr = name.substr(0, 1) + char + name.substr(-1, 1);
      } else {
        newStr = name;
      }
      return newStr;
    },
    showPreview(url) {
      ImagePreview([url]);
    },
    //获取报警详情
    async getInfo(cd) {
      if (!cd) {
        this.$notify({ type: "danger", message: "很抱歉，报警信息有误，无法上报举证内容！" });
        return;
      }
      const res = await $http.selectAlarmByCd(cd).catch(e => { console.log(e) });
      if (res && res.code === 0) {
        this.dataForm = res.data;
        this.$set(
          this.dataForm,
          "replyUrlArr",
          this.makeArr(this.dataForm.replyUrl)
        );
        if (!this.dataForm.dvMob) return;
        this.dvMob = this.dataForm.dvMob.substring(0, 7);
        if (!this.isVerified) {
          this.codeShow = true;
          this.showKeyboard = true;
        }
        // if (this.dataForm.isReply === 2) {
        //   this.$notify({ type: "primary", message: "该报警已处理" });
        // }
      } else {
        this.$notify({ type: "danger", message: res.msg });
      }
    },
    // 图片url字符串
    makeArr(str) {
      let replyUrlArr = [];
      if (str) {
        let tempArr = str.split(",");
        replyUrlArr = tempArr.map((item) => {
          return { url: item };
        });
      }
      return replyUrlArr;
    },
    // 发送验证码
    async sendCode() {
      let mob = this.dataForm.dvMob;
      if (!mob) {
        this.$notify({ type: "danger", message: "未查询到手机号" });
        return;
      }
      const res = await $http.sendsmsByAlarmPk(mob).catch(e => { console.log(e) });
      if (res && res.code === 0) {
        this.$notify({ type: "success", message: "短信发送成功!" });
        this.canSent = false;
        this.time = 60;
        const setTimeoutS = setInterval(() => {
          this.time--;
          if (this.time <= 0) {
            clearInterval(setTimeoutS);
            this.canSent = true;
          }
        }, 1000);
      } else {
        this.$dialog.alert({
          message: "短信发送失败",
        });
      }
    },
    // 验证码确认
    async codeSubmit() {
      if (this.dataForm.dvMob && this.smsCode) {
        const res = await $http.checkCode(this.dataForm.dvMob, this.smsCode).catch(e => { console.log(e) });
        if (res && res.code === 0) {
          this.codeShow = false;
          this.isVerified = true;
        }
      }
    },
    // 上传图片
    async afterRead(f) {
      // 此时可以自行将文件上传至服务器
      const _this = this;
      this.loading = true;
      const formData = new FormData();
      formData.append("file", f.file);

      let res = await $httpCommon.uploadEntp(formData).catch(e => { _this.loading = false; console.log(e) });
      this.loading = false;
      if (res && res.code === 0 && res.data) {
        this.dataForm.replyUrlArr[
          this.dataForm.replyUrlArr.length - 1
        ].url = res.data[0].fileUrl;
      }
    },
    // 提交
    async submit() {
      if (!this.dataForm.replyRemark) {
        this.$notify({ type: "danger", message: "请输入情况说明" });
        return;
      }
      if (!this.dataForm.replyUrlArr.length) {
        this.$notify({ type: "danger", message: "请上传图片" });
        return;
      }
      // 图片数组转字符串
      if (!this.dataForm.replyUrlArr.length) {
        this.$notify({ type: "danger", message: "请上传图片举证" });
        return;
      }
      let tempArr = this.dataForm.replyUrlArr.map((item) => {
        return item.url;
      });
      this.dataForm.replyUrl = tempArr.join(",");


      let res = await $http.alarmReplay(this.dataForm.cd, this.dataForm.replyRemark, this.dataForm.replyUrl).catch(e => { console.log(e) });
      if (res && res.code === 0) {
        this.$notify({
          type: "success",
          message: "提交成功",
          duration: 1500,
        });
        this.getInfo(this.dataForm.cd);
      }
    },
    onBeforeClose(action, done) {
      if (action === "confirm") {
        return done(false);
      } else {
        return done();
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.main-content {
  position: relative;
  height: 100vh;
  width: 100vw;
  background: #f7f8fa;

  .full-screen {
    width: 100%;
    height: 100%;
    z-index: 2;
    background: rgba(255, 255, 255, 0.6);
    position: fixed;

    .loading {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }
  }
}

.btn-box {
  text-align: center;

  .btn {
    width: 80%;
  }
}

::v-deep {
  .van-cell {
    .van-cell__title {
      flex: 0 180px !important;
    }
  }

  .van-number-keyboard {
    z-index: 2020;
  }
}
</style>