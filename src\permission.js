import router from "./router";
import store from "./store";
import { getPageTitle, getUrlParam } from "@/utils/common";
import { Toast, Notify } from "vant";
router.beforeEach((to, from, next) => {
  if (to?.meta?.auth) {
    let meta = to.meta || {};
    let tp = meta.type;
    // 需要token才行
    const token = store.getters.token || null;
    if (token) {
      next();
    } else {
      switch (tp) {
        case 1:
          router.push({
            path: "/entpLogin",
            query: { redirect: router.currentRoute.fullPath },
          });
          break;
        case 3:
          router.push({
            path: "/personLogin",
            query: { redirect: router.currentRoute.fullPath },
          });
          break;
        default:
          Notify({
            type: "danger",
            message: "很抱歉，您当前没有权限访问该页面！",
          });
          router.push({
            path: "/home",
            query: { redirect: router.currentRoute.fullPath },
          });
          break;
      }
    }
  } else {
    next();
  }
});

router.afterEach((to, from) => {
  // 页面标题
  document.title = getPageTitle(to.meta.title);
});
