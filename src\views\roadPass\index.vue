<template>
  <div class="road-pass-box">
    <div class="header">通行证路线</div>
  </div>
</template>

<script>
import {getRoadPassDetail} from "@/apis/roadPass.js";
export default {
  name: "",
  components: { },
  data() {
    return {
      query: {},
    };
  },
  created() {
    let params = this.$route.query;
    this.query = params;
    this.getDetail()
  },
  methods: {
    getDetail(){
      let params = {...this.query};
      getRoadPassDetail(params).then(res=>{
        console.log("res",res)
      })
    }
  },
};
</script>

<style lang="scss" scoped>
.road-pass-box{
  .header{ 
    text-align: center;
    height: 60px;
    line-height: 60px;
    font-size: 22px;
    font-weight: 600;
    color: #333;
    border-bottom: 1px solid #eee;
  }
}
</style>