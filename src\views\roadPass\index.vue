<template>
  <div class="road-pass-box">
    <!-- 信息展示区域 -->
    <div class="info-section" :class="{ collapsed: infoCollapsed }">
      <!-- 基本信息卡片 -->
      <van-card class="basic-info-card">
        <template #title>
          <div class="card-title">通行证详情</div>
        </template>
        <template #desc>
          <div class="basic-info">
            <div class="info-row">
              <span class="label">号牌：</span>
              <span class="value">{{ getPlateNumbers() }}</span>
            </div>
            <div class="info-row">
              <span class="label">有效期：</span>
              <span class="value">{{ formatDateRange(passDetail.startDate, passDetail.endDate) }}</span>
            </div>
            <div class="info-row">
              <span class="label">车辆所有人：</span>
              <span class="value">{{ passDetail.carrierName || '-' }}</span>
            </div>
          </div>
        </template>
      </van-card>

      <!-- 高峰期限制信息 -->
      <van-card class="peak-time-card">
        <template #title>
          <div class="card-title peak-title">
            <van-icon name="warning" />
            高峰期禁行时间
          </div>
        </template>
        <template #desc>
          <div class="peak-time-info">
            上午7:00-9:00，下午16:30-18:30，部分路段、车辆高峰通行时间按有关规定执行。本通行证电子上传，以二维码扫码为准。
          </div>
        </template>
      </van-card>

      <!-- 路线信息 -->
      <van-collapse v-model="routeActiveNames" class="route-collapse">
        <van-collapse-item title="禁止高峰通行路线" name="routes">
          <div class="route-list">
            <div
              v-for="(route, index) in routeList"
              :key="route.id"
              class="route-item"
              @click="highlightRoute(route, index)"
            >
              <div class="route-name">{{ route.routeName }}</div>
              <van-icon name="location" class="route-icon" />
            </div>
          </div>
        </van-collapse-item>
      </van-collapse>

      <!-- 折叠控制按钮 -->
      <div class="collapse-btn" @click="toggleInfoSection">
        <van-icon :name="infoCollapsed ? 'arrow-down' : 'arrow-up'" />
        <span>{{ infoCollapsed ? '展开信息' : '收起信息' }}</span>
      </div>
    </div>

    <!-- 地图展示区域 -->
    <div class="map-section" :class="{ expanded: infoCollapsed }">
      <div id="baiduMap" class="baidu-map"></div>
      <div v-if="loading" class="map-loading">
        <van-loading type="spinner" color="#1989fa">地图加载中...</van-loading>
      </div>
    </div>
  </div>
</template>

<script>
import {getRoadPassDetail} from "@/apis/roadPass.js";

export default {
  name: "RoadPassDetail",
  components: { },
  data() {
    return {
      query: {},
      loading: true,
      passDetail: {},
      routeList: [],
      infoCollapsed: false,
      routeActiveNames: ['routes'],
      map: null,
      routeOverlays: [], // 存储地图上的路线覆盖物
      highlightedRoute: null, // 当前高亮的路线
    };
  },
  created() {
    let params = this.$route.query;
    this.query = params;
    this.getDetail();
  },
  mounted() {
    this.$nextTick(() => {
      this.initMap();
    });
  },
  methods: {
    // 获取通行证详情
    async getDetail() {
      try {
        this.loading = true;
        let params = {...this.query};
        const res = await getRoadPassDetail(params);

        if (res && res.code === 0 && res.data) {
          this.passDetail = res.data;
          this.processRouteData();
          this.drawRoutes();
        } else {
          this.$toast('获取通行证详情失败');
        }
      } catch (error) {
        console.error('获取详情失败:', error);
        this.$toast('网络错误，请重试');
      } finally {
        this.loading = false;
      }
    },

    // 处理路线数据
    processRouteData() {
      if (this.passDetail.routes && this.passDetail.routes.length > 0) {
        this.routeList = this.passDetail.routes.map(route => ({
          ...route,
          coordinates: this.parseRouteCoordinates(route.routeLine)
        }));
      }
    },

    // 解析路线坐标数据
    parseRouteCoordinates(routeLine) {
      try {
        if (typeof routeLine === 'string') {
          const parsed = JSON.parse(routeLine);
          if (Array.isArray(parsed) && parsed.length > 0) {
            // 处理嵌套数组的情况
            return parsed.flat();
          }
        }
        return [];
      } catch (error) {
        console.error('解析路线坐标失败:', error);
        return [];
      }
    },

    // 格式化日期范围
    formatDateRange(startDate, endDate) {
      if (!startDate || !endDate) return '-';
      return `${startDate} 至 ${endDate}`;
    },

    // 获取车牌号列表
    getPlateNumbers() {
      if (!this.passDetail.vehicles || this.passDetail.vehicles.length === 0) {
        return '-';
      }
      return this.passDetail.vehicles.map(v => v.plateNo).join('、');
    },

    // 切换信息区域显示状态
    toggleInfoSection() {
      this.infoCollapsed = !this.infoCollapsed;
      // 延迟调整地图大小，确保动画完成
      setTimeout(() => {
        if (this.map) {
          this.map.getViewport();
        }
      }, 300);
    },

    // 初始化百度地图
    initMap() {
      if (typeof BMap === 'undefined') {
        console.error('百度地图API未加载');
        this.$toast('地图加载失败');
        return;
      }

      // 创建地图实例
      this.map = new BMap.Map("baiduMap");

      // 设置地图中心点为宁波市镇海区
      const centerPoint = new BMap.Point(121.66386, 30.001186);
      this.map.centerAndZoom(centerPoint, 12);

      // 启用滚轮缩放
      this.map.enableScrollWheelZoom(true);

      // 添加地图控件
      this.map.addControl(new BMap.NavigationControl());
      this.map.addControl(new BMap.ScaleControl());

      console.log('地图初始化完成');
    },

    // 绘制所有路线
    drawRoutes() {
      if (!this.map || !this.routeList.length) return;

      // 清除之前的路线
      this.clearRoutes();

      const allPoints = [];

      this.routeList.forEach((route, index) => {
        if (route.coordinates && route.coordinates.length > 0) {
          const points = route.coordinates.map(coord =>
            new BMap.Point(coord.lng, coord.lat)
          );

          // 创建路线
          const polyline = new BMap.Polyline(points, {
            strokeColor: "#1989fa",
            strokeWeight: 4,
            strokeOpacity: 0.8
          });

          // 添加路线到地图
          this.map.addOverlay(polyline);

          // 存储路线覆盖物
          this.routeOverlays.push({
            overlay: polyline,
            route: route,
            index: index
          });

          // 收集所有点用于设置视野
          allPoints.push(...points);

          // 添加点击事件
          polyline.addEventListener('click', () => {
            this.showRouteDetail(route);
          });
        }
      });

      // 设置地图视野包含所有路线
      if (allPoints.length > 0) {
        this.map.setViewport(allPoints);
      }
    },

    // 清除所有路线
    clearRoutes() {
      this.routeOverlays.forEach(item => {
        this.map.removeOverlay(item.overlay);
      });
      this.routeOverlays = [];
      this.highlightedRoute = null;
    },

    // 高亮显示指定路线
    highlightRoute(route, index) {
      // 重置所有路线样式
      this.routeOverlays.forEach(item => {
        item.overlay.setStrokeColor("#1989fa");
        item.overlay.setStrokeWeight(4);
      });

      // 高亮当前路线
      const targetOverlay = this.routeOverlays.find(item => item.index === index);
      if (targetOverlay) {
        targetOverlay.overlay.setStrokeColor("#ff4444");
        targetOverlay.overlay.setStrokeWeight(6);
        this.highlightedRoute = index;

        // 将地图中心移动到该路线
        if (route.coordinates && route.coordinates.length > 0) {
          const centerPoint = route.coordinates[Math.floor(route.coordinates.length / 2)];
          this.map.panTo(new BMap.Point(centerPoint.lng, centerPoint.lat));
        }
      }
    },

    // 显示路线详情
    showRouteDetail(route) {
      this.$dialog.alert({
        title: '路线详情',
        message: `路线名称：${route.routeName}\n禁行类型：${route.denyType === 'both' ? '双向禁行' : '单向禁行'}`
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.road-pass-box {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f7f8fa;

  // 信息展示区域
  .info-section {
    flex-shrink: 0;
    background-color: #fff;
    transition: all 0.3s ease;
    max-height: 60vh;
    overflow-y: auto;

    &.collapsed {
      max-height: 80px;
      overflow: hidden;
    }

    // 基本信息卡片
    .basic-info-card {
      margin: 16px;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .card-title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }

      .basic-info {
        .info-row {
          display: flex;
          align-items: center;
          margin-bottom: 8px;
          font-size: 14px;

          &:last-child {
            margin-bottom: 0;
          }

          .label {
            color: #666;
            min-width: 80px;
            font-weight: 500;
          }

          .value {
            color: #333;
            flex: 1;
            font-weight: 600;
          }
        }
      }
    }

    // 高峰期限制卡片
    .peak-time-card {
      margin: 0 16px 16px;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      border-left: 4px solid #ff4444;

      .peak-title {
        color: #ff4444;
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 16px;
        font-weight: 600;
      }

      .peak-time-info {
        color: #666;
        font-size: 13px;
        line-height: 1.5;
        background-color: #fff2f0;
        padding: 12px;
        border-radius: 8px;
        margin-top: 8px;
      }
    }

    // 路线折叠面板
    .route-collapse {
      margin: 0 16px;

      .route-list {
        max-height: 200px;
        overflow-y: auto;

        .route-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 12px;
          margin-bottom: 8px;
          background-color: #f8f9fa;
          border-radius: 8px;
          border: 1px solid #e9ecef;
          transition: all 0.2s ease;

          &:active {
            background-color: #e3f2fd;
            border-color: #1989fa;
          }

          .route-name {
            flex: 1;
            font-size: 14px;
            color: #333;
            font-weight: 500;
          }

          .route-icon {
            color: #1989fa;
            font-size: 16px;
          }
        }
      }
    }

    // 折叠控制按钮
    .collapse-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      padding: 12px;
      background-color: #f8f9fa;
      border-top: 1px solid #e9ecef;
      color: #666;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:active {
        background-color: #e9ecef;
      }
    }
  }

  // 地图展示区域
  .map-section {
    flex: 1;
    position: relative;
    transition: all 0.3s ease;

    &.expanded {
      flex: 1;
    }

    .baidu-map {
      width: 100%;
      height: 100%;
      min-height: 300px;
    }

    .map-loading {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background-color: rgba(255, 255, 255, 0.9);
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }
}

// 深度选择器，修改vant组件样式
:deep(.van-card) {
  background-color: transparent;

  .van-card__header {
    padding-bottom: 8px;
  }

  .van-card__content {
    padding-top: 0;
  }
}

:deep(.van-collapse-item__title) {
  font-weight: 600;
  color: #333;
}

:deep(.van-collapse-item__content) {
  padding: 12px 0;
}
</style>