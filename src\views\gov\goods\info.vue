<!--
  ** date: 2023-7-20
  ** author: zhangxx
  ** desc: 应急救援
 -->
<template>
  <div v-cloak
       class="main-content">
    <div style="padding: 0 26px;">
      <div class="basic-box bg-white"
           style="margin-bottom: 0;">
        <div class="info-title border-bottom-dashed margin-bottom-sm">
          基本信息
        </div>
        <div class="text-black text-bold margin-bottom-xsm break-all"
             style="padding: 0 6px;">
          {{ info.fullNmCn || '' }}
        </div>
        <div class="text-gray margin-bottom-xsm text-xs break-all"
             style="padding: 0 6px;">
          {{ info.fullNmEn || '' }}
        </div>
        <div class="text-gray margin-bottom-xsm break-all"
             v-if="info.chemMsdsEntity"
             style="padding: 0 6px;font-size: 16px;line-height: 20px">
          特别警示：{{ info.chemMsdsEntity.specialWarning || '' }}
        </div>
        <div class="text-gray margin-bottom-xsm break-all"
             v-if="info.chemMsdsEntity"
             style="padding: 0 6px;font-size: 16px;line-height: 20px">
          理化特性：{{ info.chemMsdsEntity.properties || '' }}
        </div>
      </div>
      <div class="row spaceBetween flex"
           style="margin-bottom: 20px;">
        <div class="basic-box1 column"
             style="flex:1;">
          <div class="info-title1 border-bottom-dashed margin-bottom-sm">
            UN编号
          </div>
          <div class="text-black text-bold"
               style="padding: 0 6px;">
            {{ info.un || "暂无" }}
          </div>
        </div>
        <div class="basic-box1 column"
             style="flex:1;">
          <div class="info-title1 border-bottom-dashed margin-bottom-sm">
            类别
          </div>
          <div class="text-black text-bold"
               style="padding: 0 6px;">
            {{ info.category || "暂无" }}
          </div>
        </div>
        <div class="basic-box1 column"
             style="flex:1;">
          <div class="info-title1 border-bottom-dashed margin-bottom-sm">
            包装类别
          </div>
          <div class="text-black text-bold"
               style="padding: 0 6px;">
            {{ info.packKind || "暂无" }}
          </div>
        </div>
        <div class="basic-box1 column"
             style="flex:1;">
          <div class="info-title1 border-bottom-dashed margin-bottom-sm">
            CAS号
          </div>
          <div class="text-black text-bold"
               style="padding: 0 6px;">
            {{ info.cas || '暂无' }}
          </div>
        </div>
      </div>
      <div class="basic-box bg-white"
           style="margin-bottom: 0;margin-top: 20px;">
        <div class="info-title border-bottom-dashed margin-bottom-sm">
          危害信息
        </div>
        <div class="text-gray margin-bottom-xsm break-all"
             style="padding: 0 6px;font-size: 16px;line-height: 20px">
          <span class="text-black">燃烧和爆炸危险性：</span>
          <template v-if="info.chemMsdsEntity">{{ info.chemMsdsEntity.harmfulBurning || '' }}</template>
        </div>
        <div class="text-gray margin-bottom-xsm break-all"
             style="padding: 0 6px;font-size: 16px;line-height: 20px">
          <span class="text-black">活性反应：</span>
          <template v-if="info.chemMsdsEntity">{{ info.chemMsdsEntity.harmfulActive || '' }}</template>
        </div>
        <div class="text-gray margin-bottom-xsm break-all"
             style="padding: 0 6px;font-size: 16px;line-height: 20px">
          <span class="text-black">健康危害：</span>
          <template v-if="info.chemMsdsEntity">{{ info.chemMsdsEntity.harmfulHealth || '' }}</template>
        </div>
      </div>
      <div class="basic-box bg-white"
           style="margin-bottom: 0;margin-top: 10px;">
        <div class="info-title border-bottom-dashed margin-bottom-sm">
          安全措施
        </div>
        <div class="text-gray margin-bottom-xsm break-all"
             style="padding: 0 6px;font-size: 16px;line-height: 20px">
          <span class="text-black">一般要求：</span>
          <template v-if="info.chemMsdsEntity">{{ info.chemMsdsEntity.securityGeneral || '' }}</template>

        </div>
        <div class="text-gray margin-bottom-xsm break-all"
             style="padding: 0 6px;font-size: 16px;line-height: 20px">
          <span class="text-black">操作安全：</span>
          <template v-if="info.chemMsdsEntity">{{ info.chemMsdsEntity.securityOpr || '' }}</template>

        </div>
        <div class="text-gray margin-bottom-xsm break-all"
             style="padding: 0 6px;font-size: 16px;line-height: 20px">
          <span class="text-black">存储安全：</span>
          <template v-if="info.chemMsdsEntity">{{ info.chemMsdsEntity.securityStorage || '' }}</template>

        </div>
        <div class="text-gray margin-bottom-xsm break-all"
             style="padding: 0 6px;font-size: 16px;line-height: 20px">
          <span class="text-black">运输安全：</span>
          <template v-if="info.chemMsdsEntity">{{ info.chemMsdsEntity.securityTransport || '' }}</template>

        </div>
      </div>
      <div class="basic-box bg-white"
           style="margin-bottom: 0;margin-top: 10px;">
        <div class="info-title border-bottom-dashed margin-bottom-sm">
          应急处置原则
        </div>
        <div class="text-gray margin-bottom-xsm break-all"
             style="padding: 0 6px;font-size: 16px;line-height: 20px">
          <span class="text-black">急救措施：</span>
          <template v-if="info.chemMsdsEntity">{{ info.chemMsdsEntity.emergencyTreatment || '' }}</template>

        </div>
        <div class="text-gray margin-bottom-xsm break-all"
             style="padding: 0 6px;font-size: 16px;line-height: 20px">
          <span class="text-black">灭火方法：</span>
          <template v-if="info.chemMsdsEntity">{{ info.chemMsdsEntity.emergencyOutfire || '' }}</template>

        </div>
        <div class="text-gray margin-bottom-xsm break-all"
             style="padding: 0 6px;font-size: 16px;line-height: 20px">
          <span class="text-black">泄露应急处置：</span>
          <template v-if="info.chemMsdsEntity">{{ info.chemMsdsEntity.emergencyReveal || '' }}</template>
        </div>
      </div>

    </div>
  </div>
</template>


<script>
import * as $httpGoods from "@/apis/vec_pers_entp_tank";
export default {
  data () {
    return {
      info: {}
    }
  },
  created () {
    let pk = this.$route.params.pk;
    $httpGoods.getGoodsInfo(pk).then(res => {
      this.info = res.chem
    })
  },
  mounted () {
  },
  methods: {


  }
}

</script>

<style lang="scss" scoped>
.main-content {
  height: 100vh;
  width: 100vw;
  background: #f7f8fa;
  box-sizing: border-box;
  text-align: left;
}

div {
  box-sizing: border-box;
  font-size: 32px;
}

.info-title {
  font-family: PingFangSC-Regular;
  font-size: 36px;
  color: #808080;
  letter-spacing: 0;
  padding-bottom: 20px;
  padding-left: 12px;
  flex: 1;
}

.info-title1 {
  font-family: PingFangSC-Regular;
  font-size: 32px;
  color: #808080;
  letter-spacing: 0;
  padding-bottom: 20px;
  padding-left: 5px;
  flex: 1;
}

.basic-box1 {
  background: #fff;
  padding: 10px;
  margin-top: 20px;
  margin-right: 10px;
  text-align: center;
}

.basic-box1:last-child {
  margin-right: 0;
}

.basic-box {
  width: 100%;
  padding: 24px 30px;
  background: #ffffff;
  margin-top: 20px;
  box-shadow: 0 0 12px 0 rgba(208, 209, 213, 0.5);
}

.column {
  display: flex;
  flex-direction: column;
}

.text-gray,
.line-gray,
.lines-gray {
  color: #aaaaaa;
}

.text-black,
.line-black,
.lines-black {
  padding-top: 10px;
  color: #333333;
}

.text-white,
.line-white,
.lines-white {
  color: #ffffff;
}

.margin-bottom-xsm {
  margin-bottom: 14px;
}

.break-all {
  word-break: break-all;
}

.border-bottom-dashed {
  border-bottom: 2px dashed #c9ccd4;
}

.row {
  display: flex;
}

.margin-bottom-sm {
  margin-bottom: 20px;
}

.column {
  display: flex;
  flex-direction: column;
}

.spaceBetween {
  justify-content: space-between;
}

.flex {
  display: flex;
}

.text-bold {
  font-weight: bold;
}
</style>