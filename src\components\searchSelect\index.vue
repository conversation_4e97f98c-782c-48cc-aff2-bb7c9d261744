<!-- @format -->
<!--
 * @Description: 搜索下拉列表组件
 * @Author: SangShuaiKang
 * @Date: 2023-07-19 15:08:11
 * @LastEditors: SangShuaiKang
 * @LastEditTime: 2023-07-24 16:12:52
-->
<template>
	<div class="SearchSelect_box">
    <slot name="search"></slot>
		<div
			class="maskLayer_box"
			v-show="statusConfig.maskLayerShow"
		>
      <div class="hint" v-show="statusConfig.listShow === 1">请输入三个字符及以上</div>
      <slot name="listContent"></slot>
      <div class="hint" v-show="statusConfig.listShow === 3">无查询结果</div>
		</div>
	</div>
</template>

<script>
import {addClass,removeClass} from "@/utils/styleTool"
export default {
	name: "SearchSelect",
  components: {},
  props: {
    statusConfig:{
      type: Object,
      default: () => {
        return {
          maskLayerShow: false,
				  listShow:0,
        }
      },
    }
  },
	data() {
    return {
      maskLayerHeight: 0,
      bodyDom:null,
    };
  },
  watch: {
    ss() {
      
    },
    statusConfig: {
      handler(newValue, oldValue) {  // newValue=新值 oldValue=旧值
        if (newValue.maskLayerShow ) {
          this.$nextTick(() => {
            this.getSearchTopHeight();
          })
        } else {
          this.maskLayerHeight = 0;
          this.removeClass();
        }
      },
      deep: true, // 开启深度监听
    },
  },
	mounted() {
    this.getSearchTopHeight();
	},
  methods: {
  /**
   * @Date: 2023-07-19 16:47:00
   * @Author: SangShuaiKang
   * @description: 修改下拉框高度
   * @return {*}
   */    
    getSearchTopHeight() {
      if (!this.maskLayerHeight) {
       
        let searchDom = document.getElementsByClassName("SearchSelect_box")[0];
        let maskLayerDom = document.getElementsByClassName("maskLayer_box")[0];
        let height = maskLayerDom.offsetHeight - searchDom.offsetHeight - searchDom.offsetTop;
        if (height > 0 && height < maskLayerDom.offsetHeight) {
          this.addClass();
          this.maskLayerHeight = height;
          maskLayerDom.style.height = this.maskLayerHeight + "px";
          this.$forceUpdate()
        }
      }
    },
    /**
     * @Date: 2023-07-24 11:02:55
     * @Author: SangShuaiKang
     * @description: 给body添加class名，禁止滚动
     * @return {*}
     */    
    addClass() {
      if (!this.bodyDom) {
        const collection = document.getElementsByTagName("body")[0];
        this.bodyDom = collection;
        addClass(collection, "prohibitScroll");
      }
    },
    /**
     * @Date: 2023-07-24 11:02:55
     * @Author: SangShuaiKang
     * @description: 给body移除class名，禁止滚动
     * @return {*}
     */    
    removeClass() {
      if (this.bodyDom) {
        removeClass(this.bodyDom, "prohibitScroll");
        this.bodyDom = null;
      }
    }

	},
};
</script>

<style lang="scss" scoped>
.SearchSelect_box {
	position: relative;
  z-index: 999;
	.maskLayer_box {
		position: absolute;
		z-index: 99999;
    left: 0;
		width: 100vw;
		height: 100vh;
    box-sizing: border-box;
    padding: 0 15px 5px;
    overflow-y: auto;
    background-color: #fff;
    .hint{
      font-size: 32px;
      text-align: center;
      line-height: 50px;
		}
	}
}
</style>
