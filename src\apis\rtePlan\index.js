/*
 * @Description: 电子运单
 * @Author: SangShuaiKang
 * @Date: 2023-07-14 11:00:11
 * @LastEditors: SangShuaiKang
 * @LastEditTime: 2023-07-25 14:30:56
 */
/** @format */

import request from "@/utils/request";

// 报警类型
export function getAreaList() {
  return request({
    url: "/whjk-entp/sys/menu/getAreaNew2",
    method: "get",
  });
}
// 获取电子运单列表
export function getRtePlanList(params, areaId) {

	return request({
		url: "/whjk-entp/rtePlan/listNoCount",
		method: "get",
		params: params,
		headers: {
			areaId: areaId,
		},
	});
}
// 模糊查询车牌号
export function getVecNoList(params) {
  return request({
    url: "/whjk-entp/vec/listVecNo",
    method: "get",
    params: params,
  });
}

// 获取电子运单回单
export function getDtlById(rtePlanPk) {
  return request({
    url: "/whjk-entp/rtePlan/getDtlById?id=" + rtePlanPk,
    method: "get",
  });
}
// 获取电子运单二维码
export function getRtePlanStr(rtePlanPk) {
  return request({
    url: "/whjk-entp/rtePlan/rtePlanStr?rtePlanPk=" + rtePlanPk,
    method: "get",
  });
}
// 提交回单
export function orderReceiptUpload(param) {
  return request({
    url: "/whjk-entp/orderreceipt/upload",
    method: "post",
    data: param,
  });
}

// 发送短信
export function sendMessages(data,areaId) {
	return request({
		url: "/whjk-entp/rtePlan/sms",
		method: "post",
		data: data,
		headers: {
			areaId: areaId,
		},
	});
}

// 是否有删除电子运单权限
export function isDeleteRtePlan() {
	return request({
		url: "/whjk-entp/rtePlan/judgeIsHavingCodeRtePlan",
		method: "get",
	});
}

// 向经办人发送删除电子运单短信验证码
export function sendDelSms() {
	return request({
		url: "/whjk-entp/code/sendDelSms",
		method: "get",
	});
}
// 验证删除电子运单短信验证码
export function checkDelCode(params) {
	return request({
		url: "/whjk-entp/code/checkDelCode",
		method: "get",
		params: params,
	});
}
// 验证删除电子运单短信验证码
export function deleteRtePlan(params, areaId) {
	return request({
		url: "/whjk-entp/rtePlan/del",
		method: "get",
		params: params,
		headers: {
			areaId: areaId,
		},
	});
}

