const { defineConfig } = require("@vue/cli-service");
const cdnDependencies = require("./dependencies.cdn");
const path = require("path");
const resolve = (dir) => path.join(__dirname, dir);
const IS_PROD = ["production", "stag"].includes(process.env.NODE_ENV);
const externals = {};
cdnDependencies.forEach((pkg) => {
  if (pkg.library) {
    externals[pkg.name] = pkg.library;
  }
});
const cdn = {
  css: cdnDependencies.map((e) => e.css).filter((e) => e),
  js: cdnDependencies.map((e) => e.js).filter((e) => e),
};

module.exports = defineConfig({
  transpileDependencies: true,
  publicPath: IS_PROD ? "/h5" : "/", // 默认'/'，部署应用包时的基本 URL
  css: {
    loaderOptions: {
      // sass: {
      //   sassOptions: {
      //     outputStyle: "expanded",
      //   },
      // },
      postcss: {
        postcssOptions: {
          plugins: [
            require("postcss-pxtorem")({
              rootValue({ file }) {
                return file?.indexOf("vant") !== -1 ? 37.5 : 75;
              },
              propList: ["*"], // 存储哪些将被转换的属性列表，这里设置为 ['*'] 全部。
              // selectorBlackList: ["van-"],
              // exclude: (e) => {
              //   // 需要把 px 转换为 rem 的文件目录/路径
              //   if (/(src(\\|\/)views(\\|\/)dashboard)/.test(e)) {
              //     return false;
              //   }
              //   return true;
              // },
            }),
          ],
        },
      },
    },
  },
  devServer: {
    open: true, // 是否打开浏览器
    hot: true, //自动保存
    allowedHosts: "all",
    proxy: {
      "/apis": {
        // 生产环境
        // target: "https://dcys.dacyun.com",
        // 预生产环境
        target: "https://stag-dcys3.dacyun.com",
        changeOrigin: true, // 是否跨域, 开启代理，在本地创建一个虚拟服务端
        secure: false, // 如果是https接口，需要配置这个参数
        ws: false, // 是否代理websockets
        pathRewrite: {
          "^/apis": "",
        },
      },
    },
  },
  chainWebpack(config) {
    config.plugin("html").tap((args) => {
      args[0].title = process.env.VUE_APP_TITLE;
      args[0].cdn = cdn;
      return args;
    });
    config.resolve.alias
      .set("@", resolve("src"))
      .set("@apis", resolve("src/apis"))
      .set("static", resolve("public"));

    config.when(IS_PROD, (config) => {
      config.optimization.splitChunks({
        chunks: "all",
        cacheGroups: {
          // cacheGroups 下可以可以配置多个组，每个组根据test设置条件，符合test条件的模块
          commons: {
            name: "chunk-commons",
            test: resolve("src/components"),
            minChunks: 3, //  被至少用三次以上打包分离
            priority: 5, // 优先级
            reuseExistingChunk: true, // 表示是否使用已有的 chunk，如果为 true 则表示如果当前的 chunk 包含的模块已经被抽取出去了，那么将不会重新生成新的。
          },
          node_vendors: {
            name: "chunk-libs",
            chunks: "initial", // 只打包初始时依赖的第三方
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
          },
          vantUI: {
            name: "chunk-vantUI", // 单独将 vantUI 拆包
            priority: 20, // 数字大权重到，满足多个 cacheGroups 的条件时候分到权重高的
            test: /[\\/]node_modules[\\/]_?vant(.*)/,
          },
        },
      });
      config.optimization.runtimeChunk("single");
    });

    config.module
      .rule("pug")
      .test(/\.pug$/)
      .use("style-px2rem-loader")
      .loader("style-px2rem-loader")
      .end();
  },
  configureWebpack: (config) => {
    if (process.env.VUE_APP_CDN == "ON") {
      config.externals = externals;
    }
    // if (process.env.VUE_APP_GZIP == "ON") {
    //   return {
    //     plugins: [
    //       new CompressionPlugin({
    //         algorithm: "gzip",
    //         test: /\.(js|css)$/, // 匹配文件名
    //         threshold: 10240, // 对超过10k的数据压缩
    //         deleteOriginalAssets: false, // 不删除源文件
    //         minRatio: 0.8, // 压缩比
    //       }),
    //     ],
    //   };
    // }
  },
});
