import request from "@/utils/request";

// 运输企业图片上传
export function uploadEntp(data) {
    return request({
        url: "/whjk-entp/sys/oss/upload",
        method: "post",
        data: data,
        headers: {
            "Content-Type": "multipart/form-data",
        },
    });
}
// 化工企业图片上传
export function uploadCp(data) {
    return request({
        url: "/whjk-cp/sys/oss/upload/multi",
        method: "post",
        data: data,
        headers: {
            "Content-Type": "multipart/form-data",
        },
    });
}

export function isbind(params) {
    return request({
        url: "/zjdc-wechat/user/isbind",
        method: "post",
        params: params,
        headers: {
            "Content-Type": "application/json",
        },
    });
}