<!--
  ** date: 2023-7-20
  ** desc: 运输企业账号绑定
 -->
<template>
  <div class="login-page">
    <div class="login-content">
      <div class="login-title-wrapper">
        <!-- <div class="login-title">{{ title }}</div> -->
        <div class="login-subtitle">{{ subtitle }}</div>
      </div>
      <div class="login-form">
        <van-form @submit="onSubmit"
                  ref="editForm"
                  class="edit-form">
          <van-field v-model="editForm.mobile"
                     type="number"
                     label=""
                     placeholder="请输入手机号"
                     :rules="$formRules({ required: true, type: 'mobile' })">
            <template #left-icon>
              <van-icon class="iconfont"
                        class-prefix='icon'
                        name='shoujihao' />
            </template>
          </van-field>
          <van-field v-model="editForm.captcha"
                     type="number"
                     center
                     clearable
                     label=""
                     placeholder="请输入手机验证码"
                     use-button-slot
                     :rules="$formRules({ required: true })">
            <template #left-icon>
              <van-icon class="iconfont"
                        class-prefix='icon'
                        name='yanzhengma' />
            </template>
            <van-button :disabled="isClick || !isValidMobile"
                        slot="button"
                        size="small"
                        type="info"
                        native-type="button"
                        @click="sendPhoneCode">{{ codeText
              }}</van-button>
          </van-field>
          <div style="margin: 16px">
            <van-button round
                        block
                        type="info"
                        native-type="submit">提交</van-button>
          </div>
        </van-form>
      </div>
    </div>
  </div>
</template>

<script>
import { Toast, Notify } from "vant";
import {
  sendMobCode,
  entpLogin,
} from "@/apis/user";
import { isMobile } from "@/utils/validate";

export default {
  name: "entpLogin",
  data () {
    return {
      title: process.env.VUE_APP_TITLE,

      subtitle: "运输企业登录",
      codeText: "获取验证码",
      isClick: false,  // 是否已点击发送验证码
      editForm: {
        mobile: "",
        captcha: "",
      },
    };
  },
  computed: {
    isValidMobile () {
      let val = this.editForm.mobile
      return val && isMobile(val)
    }
  },
  mounted () {
  },
  methods: {
    // 获取验证码
    async sendPhoneCode () {
      let mobile = this.editForm.mobile;
      let self = this;
      if (!this.isClick) {
        if (isMobile(mobile)) {
          this.isClick = true;
          const res = await sendMobCode(mobile, 1).catch(e => { console.log(e); self.isClick = false }); // 获取验证码接口
          let s = 60;
          this.codeText = s + "s";
          let interval = setInterval(() => {
            s--;
            this.codeText = s + "s";
            if (s < 0) {
              this.codeText = "重新获取";
              this.isClick = false;
              clearInterval(interval);
            }
          }, 1000);
          if (res.code !== 0) {
            this.isClick = false;
          }
        } else {
          this.isClick = false;
          Toast.fail("请输入正确的手机号码");
        }
      }
    },
    onSubmit () {
      let self = this;
      this.$refs.editForm
        .validate()
        .then(() => {
          entpLogin(self.editForm.mobile, self.editForm.captcha)
            .then((res) => {
              if (res.code == 0) {
                let token = res.token;
                self.$store.dispatch("user/setToken", token);
                Toast("登录成功！");
              } else {
                Toast("绑定失败：" + res.msg);
              }
            })
            .catch(() => { });
        })
        .catch(() => {
          Toast("手机号，验证码填写不规范");
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.login-page {
  position: relative;
  width: 100%;
  height: 100%;
  background: url('~static/images/home/<USER>') no-repeat 0 0;
  background-size: 100% auto;

  .login-content {
    position: absolute;
    width: 690px;
    height: 638px;
    top: 292px;
    left: 50%;
    transform: translateX(-50%);
    background: #fff;
    box-shadow: 0px 1px 20px 0px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    padding-top: 60px;

    .login-title-wrapper {
      font-weight: 500;
      color: #0f50e6;
      text-align: center;

      .login-title {
        font-size: 30px;
        line-height: 58px;

        &::after {
          width: 514px;
          height: 40px;
          background: #2361e3;
          opacity: 0.15;
        }
      }

      .login-subtitle {
        font-size: 42px;
        line-height: 58px;
      }
    }

    .login-form {
      margin: 50px;

      .edit-form {
      }
    }
  }
}

.iconfont {
  font-size: 44px;
}
</style>
