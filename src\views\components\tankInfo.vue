<!--
  ** date: 2023-08-08
  ** author: fanls
  ** desc: 罐体详情
 -->
<template>
  <div class="weui_msg">
    <van-cell-group v-if="tankInfo">
      <van-cell
        title="基本信息"
        style="background-color: #f7f8fa; color: #c8c9cc"
      >
        <template>
          <span class="custom-title">
            <template v-if="tankInfo.basicHandleFlag == ''"
              ><span style="color: red">未提交</span></template
            >
            <template v-else-if="tankInfo.basicHandleFlag === '1'"
              ><span style="color: #67c23a">审核通过</span>
            </template>
            <template v-else-if="tankInfo.basicHandleFlag === '2'">
              <span style="color: red">审核未通过，原因：</span>
              <template v-if="tankInfo.basicHandleRemark">{{
                tankInfo.basicHandleRemark
              }}</template>
              <template v-else>无</template>
            </template>
            <template v-else-if="tankInfo.basicHandleFlag === '0'"
              ><span style="color: #ffce42">待受理</span></template
            >
          </span>
        </template>
      </van-cell>
      <van-cell title="罐体类型" :value="tankInfo.tankType"> </van-cell>
      <van-cell title="所属公司" :value="tankInfo.ownedCompany"> </van-cell>
      <van-cell title="罐体体积(m³)" :value="tankInfo.volume"> </van-cell>
      <van-cell title="产品名称" :value="tankInfo.prdtNm"> </van-cell>
      <van-cell title="最大允许充装量(Kg)" :value="tankInfo.filWeight">
      </van-cell>
      <van-cell title="关联挂车号" :value="tankInfo.traiNo"> </van-cell>
      <van-cell title="投运/制造日期" :value="tankInfo.crtTm"> </van-cell>
      <van-cell title="制造单位" :value="tankInfo.manuFact"> </van-cell>
      <van-cell title="检验机构" :value="tankInfo.ispctOrg"> </van-cell>
      <van-cell title="装运介质" :value="tankInfo.medProp"> </van-cell>
      <van-cell title="罐体设计温度(℃)" :value="tankInfo.designTemperature">
      </van-cell>
      <van-cell title="罐体使用年限(年)" :value="tankInfo.serviceLife">
      </van-cell>

      <van-cell
        title="证件信息"
        style="background-color: #f7f8fa; color: #c8c9cc"
      >
      </van-cell>
      <div v-for="(item, index) in tankItems" :key="index" class="custom">
        <van-cell :title="item.licCatNmCn">
          <template>
            <span class="custom-title">
              <template v-if="item.handleFlag == ''"
                ><span style="color: red">未提交</span></template
              >
              <template v-else-if="item.handleFlag === '1'"
                ><span style="color: #67c23a">审核通过</span>
              </template>
              <template v-else-if="item.handleFlag === '2'">
                <span style="color: red">审核未通过，原因：</span>
                <template v-if="item.handleRemark">{{
                  item.handleRemark
                }}</template>
                <template v-else>无</template>
              </template>
              <template v-else-if="item.handleFlag === '0'"
                ><span style="color: #ffce42">待受理</span></template
              >
            </span>
          </template>
        </van-cell>
        <div class="img_box">
          <div class="id_title" v-if="item.licVldTo">
            有效期：{{ item.licVldTo }}
          </div>
          <div class="id_box">
            <div
              class="id_front"
              v-for="(el, elIndex) in item.subItems"
              :key="elIndex"
              @click="onImgPreView(el.thumbnailUrl)"
            >
              <div class="img_text">{{ item.licCatNmCn }}</div>
              <div class="img">
                <van-image
                  v-if="el.thumbnailUrl"
                  width="100%"
                  height="100%"
                  :src="el.thumbnailUrl"
                />
              </div>
            </div>
          </div>
        </div>
        <div v-if="index + 1 < tankItems.length" class="custom-else"></div>
      </div>
    </van-cell-group>
  </div>
</template>

<script>
import * as $http from "@/apis/vec_pers_entp_tank.js";
import { ImagePreview } from "vant";

export default {
  data() {
    return {
      tankInfo: {},
      tankItems: [],
    };
  },
  created() {
    let pk = this.$route.query.pk || "";
    // let pk = "4063055351595686";
    this.init(pk);
  },
  methods: {
    init(pk) {
      $http.getTankInfo(pk).then((res) => {
        if (res.code === 0 && res.data) {
          this.tankInfo = res.data.tank;
          this.tankItems = res.data.items;
        }
      });
    },
    // 预览图片
    onImgPreView(url) {
      ImagePreview({
        images: [url],
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.weui_msg {
  width: 100vw;
  height: 100vh;
}
.custom {
  // margin-bottom: 20px;
}
.custom-else {
  width: 100vw;
  height: 30px;
  background: rgb(233, 233, 233);
}
.img_box {
  width: 90%;
  height: 252px;
  padding: 15px;
}
.id_title {
  color: #f8121c;
  font-size: 28px;
}
.id_box {
  width: 100%;
  height: 100%;
  display: flex;
  overflow-x: auto;
}
.id_front {
  margin-top: 15px;
  min-width: 29.5%;
  width: 29.5%;
  margin-right: 3%;
  height: 75%;
}
.img {
  width: 100%;
  height: 80%;
  background: url("~static/images/emergency/camera.png") no-repeat center center;
  background-size: 100% 100%;
}
.img_text {
  margin-bottom: 10px;
  font-size: 18px;
}
</style>
