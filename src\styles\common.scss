/* 公共默认样式 */

[v-cloak] {
  display: none !important;
}

body {
  font-size: 28px !important;
  font-family: <PERSON><PERSON>, "Microsoft Yahei", "Lantinghei SC", "Open Sans", "Hiragino Sans GB", "STHeiti", "WenQuanYi Micro Hei", Sim<PERSON><PERSON>, sans-serif;
}
.prohibitScroll{
  overflow: hidden !important;
}

/*清浮动*/
.clearfix {
  display: block;

  &:before,
  &:after {
    content: "";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
  }
}

/*必填项*/
.required:after {
  content: "*";
  position: relative;
  top: -10px;
  color: #e00;
  font-size: 12px;
}

.hide,
.hidden {
  display: none !important;
}

// 浮动
.ft {
  &-lf {
    float: left;
  }

  &-rt {
    float: right;
  }
}

// 文本位置
.align {
  &-left {
    text-align: left;
  }

  &-center {
    text-align: center;
  }

  &-right {
    text-align: right;
  }
}

// 背景色
.bg {
  /*红色*/
  &-red {
    background-color: #ea6291 !important;
  }

  /*黄色*/
  &-yellow {
    background-color: #fbb12d !important;
  }

  /*绿色*/
  &-green {
    background-color: #8fc155 !important;
  }

  /*蓝色*/
  &-blue {
    background-color: #2facf1 !important;
  }

  /*灰色*/
  &-gray {
    background-color: #8e8e8e !important;
  }

  /*紫色*/
  &-purple {
    background-color: #8e8e8e !important;
  }

  /*橙色*/
  &-orange {
    background-color: #fb6e52 !important;
  }
}
