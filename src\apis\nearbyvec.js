import request from "@/utils/request";

export function findNearByGpsZH(params) {
    return request({
        url: "/zh/whjk-gov/gps/findNearByGps",
        method: "get",
        params: params,
        headers: {
            "Content-type": "application/json;charset=UTF-8",
        },
    });
}

export function findNearByGpsSY(params) {
    return request({
        url: "/sy/whjk-gov/gps/findNearByGps",
        method: "get",
        params: params,
        headers: {
            "Content-type": "application/json;charset=UTF-8",
        },
    });
}

export function getCurrentRtePlanZH(vehicleNo) {
    return request({
        url: "/zh/whjk-gov/rtePlan/currentRtePlan?tracCd=" + vehicleNo,
        method: "get",
        // params: params,
        headers: {
            "Content-type": "application/json;charset=UTF-8",
        },
    });
}

export function getCurrentRtePlanSY(vehicleNo) {
    return request({
        url: "/sy/whjk-gov/rtePlan/currentRtePlan?tracCd" + vehicleNo,
        method: "get",
        // params: params,
        headers: {
            "Content-type": "application/json;charset=UTF-8",
        },
    });
}


export function getAlarmTodayByVecNoZH(vehicleNo) {
    return request({
        url: "/zh/whjk-gov/alarm/alarmTodayByVecNo?vecNo=" + vehicleNo,
        method: "get",
        // params: params,
        headers: {
            "Content-type": "application/json;charset=UTF-8",
        },
    });
}

export function getAlarmTodayByVecNoSY(vehicleNo) {
    return request({
        url: "/sy/whjk-gov/alarm/alarmTodayByVecNo?vecNo=" + vehicleNo,
        method: "get",
        // params: params,
        headers: {
            "Content-type": "application/json;charset=UTF-8",
        },
    });
}
