import request from "@/utils/request";

/**
 * rteplanInfo
 *电子运单详情
 *
 * @export
 * @data {*} data 运单号
 * @returns
 */
export function getrtePlanInfo(param) {
  return request({
    // url: "/whjk-entp/rtePlan/getRtePlanInfo/" + data,
    url: "/whjk-entp/rtePlan/getDtlById",
    method: "get",
    params: param,
  });
}

/**
 * rteplanInfo
 *电子运单详情二维码
 *
 * @export
 * @params {*} rtePlanPk
 * @returns
 */
export function createdQRCode(params) {
  return request({
    url: "/whjk-entp/rtePlan/rtePlanStr",
    method: "get",
    params: params,
  });
}

/**
 * rteplanInfoGov
 *电子运单详情
 *
 * @export
 * @params {*} params
 * @returns
 */
export function getrtePlanInfoGov(params) {
  return request({
    url: "/zh/whjk-gov/rtePlan/getDtlById",
    method: "get",
    params: params,
    headers: {
      // areaId: "330211",
    },
  });
}

/**
 * rteplanInfoGov
 *电子运单详情二维码
 *
 * @export
 * @params {*} rtePlanPk
 * @returns
 */
export function createdQRCodeGov(params) {
  return request({
    url: "/zh/whjk-gov/rtePlan/rtePlanStr",
    method: "get",
    params: params,
  });
}
