/**
 * 驾押人员菜单配置列表
 *
 * 注意：路由中auth:true 是说明当前页面是需要有权限才能查看的,一般游客用户是不能进入查看
 * type:1是代表企业用户，type:3代表驾押人员
 */
const routes = [
  {
    path: "/rteplanInfoGov.html",
    name: "rteplanInfoGov",
    component: () =>
      import(
        /* webpackChunkName: "gov" */ "@/views/gov/rteplan/rteplanInfoGov.vue"
      ),
    meta: { title: "电子运单详情", auth: false },
  },
  {
    path: "/violation-record.html",
    name: "violationRecord",
    component: () =>
      import(/* webpackChunkName: "gov" */ "@/views/gov/violation/record.vue"),
    meta: { title: "违章记录", auth: false },
  },
  {
    path: "/notify/alarmMap.html",
    name: "alarmMap",
    component: () =>
      import(/* webpackChunkName: "gov" */ "@/views/gov/notify/alarmMap.vue"),
    meta: { title: "车辆报警", auth: false },
  },
      //应急救援页
      {
        path: "/emergency/index.html",
        name: "emergency",
        component: () =>
          import(/* webpackChunkName: "sos" */ "@/views/gov/emergency/index.vue"),
        meta: { title: "应急救援", wechatAuth: true },
      },
];

export default routes;
