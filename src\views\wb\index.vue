<!--
  ** date: 2023-8-14
  ** desc: 化工企业扫码上传磅单
 -->
<template>
  <div class="page-content">
    <div>
      <van-nav-bar class="navbar" title="上传磅单">
      </van-nav-bar>
      <van-cell-group>
        <van-cell title="企业名称" :value="entpName" class="label-title" />
        <van-cell title="牵引车号" :value="vecNo" class="label-title" />
      </van-cell-group>
      <van-cell-group style="margin-top: 10px">
        <van-cell @click="exampleImg()" :title="`磅单图片（${dataList.length || 0}/10）`" value="查看示例图"
          label="图片要求：包含车牌，皮重、毛重、净重等信息" value-class="demo_word" label-class="desc_word" />
      </van-cell-group>
      <div class="img-wrap">
        <van-uploader :after-read="afterRead" @delete="deleteImg" v-model="dataList" multiple :max-count="10" />
      </div>
      <div style="margin: 16px;">
        <van-button type="info" block round @click="uploadArgmtUrl">提交磅单照片</van-button>
      </div>
    </div>
  </div>
</template>

<script>
import * as $httpCommon from "@/apis/common.js"
import * as $http from "@/apis/wb.js"
import { Notify, Toast } from "vant";
import { ImagePreview } from 'vant';
export default {
  name: "wb",
  data() {
    return {
      color: '#0081ff',
      entpName: "",
      vecNo: "",
      rtePlanCd: null,
      dataList: [],
      loadModal: false,
      imageIndex: 100,
      fileList: []
    };
  },
  created() {
    let query = this.$route.query
    // let query = {
    //   argmtWtPk: 5847755722380288,
    //   argmtPk:4073263317081429,
    //   token: 'dcp-sid-a4c43d64-f8d1-498a-a3e5-4529e9bcaf44'
    // }
    let token = query.token
    this.$store.dispatch("user/setToken", token);
    // uni.setStorageSync("uploadToken", token)
    if (query.argmtWtPk) {
      this.getArgmtWtInfo(query.argmtWtPk, token)
    }
    if (query.argmtPk) {
      this.getRteInfo(query.argmtPk, token)
    }
  },
  methods: {
    //获取磅单信息
    getArgmtWtInfo(pk, token) {
      $http.getArgmtWtDetail(pk, token).then(res => {
        if (res.code == 0) {
          let data = res.argmwt
          if (data.wbPic) {
            let arr = data.wbPic.split(",")
            for (let i = 0; i < arr.length; i++) {
              this.dataList.push({
                url: arr[i],
                loading: false,
                index: i,
                fail: false
              })
            }
          }
        }
      }).catch(err => { });
    },
    //获取运单信息
    getRteInfo(pk, token) {
      $http.getRteDetail(pk, token).then(res => {
        if (res.code == 0) {
          let data = res.data
          this.entpName = data.carrierNm
          this.vecNo = data.tracCd
          this.rtePlanCd = data.cd
        }
      }).catch(err => { });
    },
    //查看示例图
    exampleImg() {
      ImagePreview([
        `${this.STATIC_URL}images/wb/bills.jpg`,
        `${this.STATIC_URL}images/wb/vecOnWb.jpg`,
      ]);
    },
    //上传磅单
    uploadArgmtUrl() {
      var arr = []
      if (this.rtePlanCd) {
        // 未上传图片
        if (this.dataList.length == 0 || !this.fileList.length) {
          Notify({ type: "danger", message: "请上传磅单" });
          return false
        } else {
          // 判断图片是否完全全部上传完成
          let loadingFlag = false
          let failFlag = false
          for (let i = 0; i < this.dataList.length; i++) {
            if (this.dataList[i].loading) {
              loadingFlag = true
              break
            }
          }
          for (let i = 0; i < this.dataList.length; i++) {
            if (this.dataList[i].fail) {
              failFlag = true
              break
            }
          }
          if (loadingFlag) {
            Notify({ type: "warning", message: "请上传磅单" });
            return false
          } else if (failFlag) {
            Notify({ type: "danger", message: "有图片上传失败，请点击重新上传" });
            return false
          } else {
            // arr = this.dataList.map(item => {
            //   return item.url
            // })
            arr = this.fileList.map(item => {
              return item
            })
          }
        }
        var param = {
          rtePlanCd: this.rtePlanCd,
          argmtUrl: arr.join(",")
        }

        $http.uploadArgmtUrl(param).then(res => {
          if (res.code == 0) {
            this.$router.push({ path: "/wb/success", });
          }
        }).catch(err => { });
      } else {
        Notify({ type: "danger", message: "对不起，无法提交" });
      }
    },
    //上传图片
    afterRead(file) {
      // 此时可以自行将文件上传至服务器
      let data = {
        file: file.file,
      }
      file.status = 'uploading';
      file.message = '上传中...';
      $httpCommon.uploadCp(data).then((res) => {
        if (res.code == 0 && res.data?.length) {
          let data = res.data
          // 请求成功后返回图片链接
          this.fileList.push(data[0].waterMarkUrl || data[0].fileUrl)
          file.status = 'done';
        } else {
          file.status = 'failed';
          file.message = '上传失败';
          Notify({ type: "danger", message: "图片上传失败：" + res.msg });
        }
      }).catch((err) => {
        file.status = 'failed';
        file.message = '上传失败';
        Notify({ type: "danger", message: "图片上传失败:" + err });
      })

    },
    //删除图片
    deleteImg(file, detail) {
      this.fileList.splice(detail.index, 1)
    }
  },
};
</script>

<style lang="scss" scoped>
.page-content {
  height: 100vh;
  width: 100vw;
  overflow-y: auto;

  // 自定义cell title宽度
  .label-title {
    .van-cell__title {
      flex: 0 150px;
    }
  }
}

.navbar {
  background: #366EFF;

  ::v-deep {
    .van-nav-bar__title {
      color: #fff;
    }
  }
}

.demo_word {
  color: #47a4ff;
}

.desc_word {
  color: #ee0000;
  white-space: nowrap;
}

.img-wrap {
  padding: 0 32px;
}
</style>