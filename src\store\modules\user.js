import { getUserInfo } from "@/apis/user";
import {
  getToken,
  setToken,
  removeToken,
  getUserCache,
  removeUserCache,
} from "@/utils/auth";
const state = {
  token: getToken(), // token
  userInfo: getUserCache(), // 用户登录信息
};

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token;
  },
  SET_USERINFO: (state, userInfo) => {
    state.userInfo = userInfo;
  },
};

const actions = {
  // getUserInfo({ commit }) {
  //   return new Promise((resolve, reject) => {
  //     getUserInfo()
  //       .then((res) => {
  //         commit("SET_USERINFO", setUserCache(res.data));
  //         resolve(res);
  //       })
  //       .catch((err) => {
  //         reject(err);
  //       });
  //   });
  // },
  setToken({ commit }, token) {
    // 设置token
    commit("SET_TOKEN", setToken(token));
  },
  // 登出
  fedLogOut() {
    // 删除token，用户信息，登陆状态
    removeToken();
    removeUserCache();
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
