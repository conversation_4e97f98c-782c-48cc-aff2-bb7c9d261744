import request from "@/utils/request";


export function getVecFuzzyBw(params) {
    return request({
        url: "/zh/whjk-gov/vec/fuzzyBw",
        method: "get",
        params: params,
        headers: {
            "Content-type": "application/json;charset=UTF-8",
        },
    });
}



export function getGpsHistory(params) {
    return request({
        url: "/zh/whjk-gov/gps/history",
        method: "get",
        params: params,
        headers: {
            "Content-type": "application/json;charset=UTF-8",
        },
    });
}


export function getGpsTrace(params) {
    return request({
        url: "/zh/whjk-gov/gps/trace",
        method: "get",
        params: params,
        headers: {
            "Content-type": "application/json;charset=UTF-8",
        },
    });
}

export function getCurrentRtePlan(params) {
    return request({
        url: "/zh/whjk-gov/rtePlan/currentRtePlan",
        method: "get",
        params: params,
        headers: {
            "Content-type": "application/json;charset=UTF-8",
        },
    });
}