/**
 * 运输企业菜单配置列表
 *
 * 注意：路由中auth:true 是说明当前页面是需要有权限才能查看的,一般游客用户是不能进入查看
 * type:1是代表企业用户，type:3代表驾押人员
 */
const routes = [
  {
    path: "/rteplanInfo.html",
    name: "rteplanInfo",
    component: () =>
      import(/* webpackChunkName: "entp" */ "@/views/rtePlan/rtePlanInfo.vue"),
    meta: { title: "电子运单详情", auth: true, type: 1 },
  },
  {
    path: "/pages/personInfo.html",
    name: "personInfo",
    component: () =>
      import(
        /* webpackChunkName: "entp" */ "@/views/components/personInfo.vue"
      ),
    meta: { title: "人员详情", auth: true, type: 1 },
  },
  {
    path: "/pages/vecInfo.html",
    name: "vecInfo",
    component: () =>
      import(/* webpackChunkName: "entp" */ "@/views/components/vecInfo.vue"),
    meta: { title: "车辆详情", auth: true, type: 1 },
  },
  {
    path: "/pages/entpInfo.html",
    name: "entpInfo",
    component: () =>
      import(/* webpackChunkName: "entp" */ "@/views/components/entpInfo.vue"),
    meta: { title: "企业详情", auth: true, type: 1 },
  },
  {
    path: "/pages/tankInfo.html",
    name: "tankInfo",
    component: () =>
      import(/* webpackChunkName: "entp" */ "@/views/components/tankInfo.vue"),
    meta: { title: "罐体详情", auth: true, type: 1 },
  },
  {
    path: "/pages/goodsInfo.html",
    name: "goodsInfo",
    component: () =>
      import(/* webpackChunkName: "entp" */ "@/views/gov/goods/info.vue"),
    meta: { title: "货物详情", auth: true, type: 1 },
  },
];
export default routes;
