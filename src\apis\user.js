import request from "@/utils/request";

/**
 * 企业账号登录
 * @param {*} mobile 手机号
 * @param {*} captcha 验证码
 * @returns
 */
export function entpLogin(mobile, captcha) {
  return request({
    url: "/whjk-entp/sys/login",
    method: "post",
    data: JSON.stringify({
      mobile,
      captcha,
      type: "mob",
    }),
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
/**
 * 发送验证码
 * @param {string} mobile
 * @param {number} type 1：企业，3：驾押人员，2已废弃
 * @returns
 */
export function sendMobCode(mobile, type) {
  if (type !== 1 && type !== 3) {
    console.error("发送验证码功能传参错误");
    throw new Error("发送验证码功能传参错误");
  }
  return request({
    url: "/whjk-entp/code/getSmsCodeOnlyMob",
    method: "post",
    params: { mob: mobile, type: type },
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
