//判断是不是数组
export function isArray(param) {
  return Object.prototype.toString.call(param) === "[object Array]";
}
//判断是不是数组
export function isObject(param) {
  return Object.prototype.toString.call(param) === "[object Object]";
}
//判断是不是字符串
export function isString(param) {
  return Object.prototype.toString.call(param) === "[object String]";
}
//判断是不是数字
export function isNumber(param) {
  return Object.prototype.toString.call(param) === "[object Number]";
}
//判断是不是布尔
export function isBoolean(param) {
  return Object.prototype.toString.call(param) === "[object Boolean]";
}
//判断是不是Undefined
export function isUndefined(param) {
  return Object.prototype.toString.call(param) === "[object Undefined]";
}
//判断是不是Null
export function isNull(param) {
  return Object.prototype.toString.call(param) === "[object Null]";
}
//判断是不是function
export function isFunction(param) {
  return Object.prototype.toString.call(param) === "[object Function]";
}
//判断是不是日期类型
export function isDate(param) {
  return Object.prototype.toString.call(param) === "[object Date]";
}
//判断各种数据类型是不是为空 空对象 空字符串 空数组等等
export function isEmpty(param) {
  try {
    //判断是不是null 或者Undefined
    if (this.isNull(param) || this.isUndefined(param)) {
      return false;
    }
    //判断数字是不是NAN
    if (this.isNumber(param)) {
      if (isNaN(param)) {
        return false;
      } else {
        return true;
      }
    }
    //判断是不是布尔 函数 日期 正则 是则返回true
    if (
      this.isBoolean(param) ||
      this.isFunction(param) ||
      this.isDate(param) ||
      this.isRegExp(param)
    ) {
      return true;
    }
    //判断是不是是字符串，去空 长度为o返回false
    if (this.isString(param)) {
      if (param.trim().length == 0) {
        return false;
      } else {
        return true;
      }
    }
    //判断是否是数组，数组为空返回0
    if (this.isArray(param)) {
      if (param.length == 0) {
        return false;
      } else {
        return true;
      }
    }
    //判断是否是对象，判断是否是空对象是则返回false
    if (this.isObject(param)) {
      //判断对象属性个数
      if (Object.getOwnPropertyNames(param).length == 0) {
        return false;
      } else {
        return true;
      }
    }
  } catch (e) {
    console.log(e);
    return false;
  }
}

/**
 * 判断是否为空
 */
export function isNoVal(val) {
  if (typeof val == "boolean") {
    return false;
  }
  if (typeof val == "number") {
    return false;
  }
  if (val instanceof Array) {
    if (val.length == 0) return true;
  } else if (val instanceof Object) {
    if (JSON.stringify(val) === "{}") return true;
  } else {
    if (
      val == "null" ||
      val == null ||
      val == "undefined" ||
      val == undefined ||
      val == ""
    )
      return true;
    return false;
  }
  return false;
}

/**
 * URL地址
 * @param {string} s
 */
export function isURL(s) {
  return /^http[s]?:\/\/.*/.test(s);
}

/**
 * 检查是否是小写字母
 * @param str
 * @returns {boolean}
 */
export function isLowerCase(str) {
  const reg = /^[a-z]+$/;
  return reg.test(str);
}

/**
 * 检查是否是大写字母
 * @param str
 * @returns {boolean}
 */
export function isUpperCase(str) {
  const reg = /^[A-Z]+$/;
  return reg.test(str);
}

/**
 * 检查是否是字母
 * @param str
 * @returns {boolean}
 */
export function isAlphabets(str) {
  const reg = /^[A-Za-z]+$/;
  return reg.test(str);
}

/**
 * 检查是否是ios
 * @returns {boolean}
 */
export function isIOS() {
  let isIphone = navigator.userAgent.includes("iPhone");
  let isIpad = navigator.userAgent.includes("iPad");
  return isIphone || isIpad;
}

/**
 *  判断是否是微信内置浏览器
 * @returns
 */
export function isWechat() {
  const ua = window.navigator.userAgent.toLowerCase();
  if (ua.match(/MicroMessenger/i) == "micromessenger") {
    return true;
  } else {
    return false;
  }
}

/**
 * 邮箱
 * @param {string} s
 */
export function isEmail(s) {
  return /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+((.[a-zA-Z0-9_-]{2,3}){1,2})$/.test(
    s
  );
}

/**
 * 手机号码
 * @param {string} s
 */
export function isMobile(s) {
  // return /^1[0-9]{10}$/.test(s);
  return /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/.test(
    s
  );
}

/**
 * 电话号码
 * @param {string} s
 */
export function isPhone(s) {
  return /^([0-9]{3,4}-)?[0-9]{7,8}$/.test(s);
}
/**
 * 判断身份证号码
 */
export function isID(code) {
  let result = false;
  let msg = "";
  var city = {
    11: "北京",
    12: "天津",
    13: "河北",
    14: "山西",
    15: "内蒙古",
    21: "辽宁",
    22: "吉林",
    23: "黑龙江 ",
    31: "上海",
    32: "江苏",
    33: "浙江",
    34: "安徽",
    35: "福建",
    36: "江西",
    37: "山东",
    41: "河南",
    42: "湖北 ",
    43: "湖南",
    44: "广东",
    45: "广西",
    46: "海南",
    50: "重庆",
    51: "四川",
    52: "贵州",
    53: "云南",
    54: "西藏 ",
    61: "陕西",
    62: "甘肃",
    63: "青海",
    64: "宁夏",
    65: "新疆",
    71: "台湾",
    81: "香港",
    82: "澳门",
    91: "国外 ",
  };
  if (!isNoVal(code)) {
    if (code.length == 18) {
      if (!code || !/(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(code)) {
        msg = "证件号码格式错误";
      } else if (!city[code.substr(0, 2)]) {
        msg = "地址编码错误";
      } else {
        //18位身份证需要验证最后一位校验位
        code = code.split("");
        //∑(ai×Wi)(mod 11)
        //加权因子
        var factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
        //校验位
        var parity = [1, 0, "X", 9, 8, 7, 6, 5, 4, 3, 2, "x"];
        var sum = 0;
        var ai = 0;
        var wi = 0;
        for (var i = 0; i < 17; i++) {
          ai = code[i];
          wi = factor[i];
          sum += ai * wi;
        }
        if (parity[sum % 11] != code[17]) {
          msg = "证件号码校验位错误";
        } else {
          result = true;
        }
      }
    } else {
      msg = "证件号码长度不为18位";
    }
  } else {
    msg = "证件号码不能为空";
    result = true;
  }
  return result;
}
/**
 * 判断车牌号是否正确License Plate Number
 * @param {车牌号} s
 */
export function isLPN(s) {
  const regx =
    /(^[京津冀晋蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼渝川贵云藏陕甘青宁新]{1}[A-Z0-9]{6}$)|(^[A-Z]{2}[A-Z0-9]{2}[A-Z0-9\u4E00-\u9FA5]{1}[A-Z0-9]{4}$)|(^[\u4E00-\u9FA5]{1}[A-Z0-9]{5}[挂学警军港澳]{1}$)|(^[A-Z]{2}[0-9]{5}$)|(^(08|38){1}[A-Z0-9]{4}[A-Z0-9挂学警军港澳]{1}$)/;
  return regx.test(s);
}
// 车架号
export function isVIN(s) {
  return /^[0-9a-zA-Z]{17}$/.test(s);
}
// 统一社会信用代码
export function isUscCd(s) {
  return /^[0-9a-zA-Z]{15,18}$/.test(s);
}
