<!--
  * @date: 2023-08-07
  * @author:fanls
  * @desc:电子运单详情
 -->
<template>
  <div class="main-content">
    <van-collapse v-model="activeName">
      <template>
        <div class="rteplanInfo" style="">
          <van-field
            readonly
            v-model="dataList.crtTm"
            label="创建时间"
            placeholder="创建时间"
          />
          <van-field
            readonly
            v-model="dataList.updTm"
            label="更新时间"
            placeholder="更新时间"
          />
          <van-field
            readonly
            v-model="dataList.vecDespTm"
            label="起运日期"
            placeholder="起运日期"
          />
          <van-field
            readonly
            v-model="dataList.cd"
            label="运单号"
            placeholder="运单号"
          />
        </div>
      </template>
      <template v-for="(item, index) in activeTitle">
        <van-collapse-item
          class="collapse"
          :icon="item.icon"
          :key="index"
          :title="item.title"
          :name="item.title"
        >
          <div class="basic" v-show="item.title == '承运方'">
            <van-field
              readonly
              v-model="dataList.carrierNm"
              label="单位名称"
              placeholder="单位名称"
              type="textarea"
              rows="1"
              autosize
            />
            <van-field
              readonly
              v-model="dataList.carrierUscCd"
              label="统一信用代码"
              placeholder="统一信用代码"
            />
            <van-field
              readonly
              v-model="dataList.erNm"
              label="负责人"
              placeholder="负责人"
            />
            <van-field
              readonly
              v-model="dataList.erMob"
              label="负责人电话"
              placeholder="负责人电话"
            />
          </div>

          <div class="basic" v-show="item.title == '牵引车'">
            <van-field
              readonly
              v-model="dataList.tracCd"
              label="车牌号(头)"
              placeholder="车牌号(头)"
            />
            <van-field
              readonly
              v-model="dataList.tracOpraLicNo"
              label="道路运输证号"
              placeholder="道路运输证号"
            />
            <van-field
              readonly
              v-model="dataList.tracWeight"
              label="牵引车质量"
              placeholder="牵引车质量"
            />
          </div>

          <div class="basic" v-show="item.title == '挂车'">
            <van-field
              readonly
              v-model="dataList.traiCd"
              label="车牌号(挂)"
              placeholder="车牌号(挂)"
            />
            <van-field
              readonly
              v-model="dataList.traiOpraLicNo"
              label="道路运输证号"
              placeholder="道路运输证号"
            />
            <van-field
              readonly
              v-model="dataList.traiWeight"
              label="挂车核准质量"
              placeholder="挂车核准质量"
            />
          </div>

          <div class="basic" v-show="item.title == '罐体'">
            <van-field
              readonly
              v-model="dataList.tankNum"
              label="罐体编号"
              placeholder="罐体编号"
            />
            <van-field
              readonly
              v-model="dataList.tankVolume"
              label="罐体容积(m³)"
              placeholder="罐体容积(m³)"
            />
          </div>

          <div class="basic" v-show="item.title == '驾驶员'">
            <van-field
              readonly
              v-model="dataList.dvNm"
              label="姓名"
              placeholder="姓名"
            />
            <van-field
              readonly
              v-model="dataList.dvCd"
              label="从业资格证"
              placeholder="从业资格证"
            />
            <van-field
              readonly
              v-model="dataList.dvMob"
              label="联系电话"
              placeholder="联系电话"
            />
          </div>

          <div class="basic" v-show="item.title == '押运员'">
            <van-field
              readonly
              v-model="dataList.scNm"
              label="姓名"
              placeholder="姓名"
            />
            <van-field
              readonly
              v-model="dataList.scCd"
              label="从业资格证"
              placeholder="从业资格证"
            />
            <van-field
              readonly
              v-model="dataList.scMob"
              label="联系电话"
              placeholder="联系电话"
            />
          </div>

          <div class="basic" v-show="item.title == '委托方'">
            <van-field
              readonly
              v-model="dataList.consignorAddr"
              label="单位"
              placeholder="单位"
              type="textarea"
              rows="1"
              autosize
            />
            <van-field
              readonly
              v-model="dataList.consignorTel"
              label="联系方式"
              placeholder="联系方式"
            />
          </div>

          <div class="basic" v-show="item.title == '装货方'">
            <van-field
              readonly
              v-model="dataList.csnorWhseAddr"
              label="企业"
              placeholder="企业"
              type="textarea"
              rows="1"
              autosize
            />
            <van-field
              readonly
              v-model="dataList.csnorWhseDist"
              label="地址"
              placeholder="地址"
              type="textarea"
              rows="1"
              autosize
            />
            <van-field
              readonly
              v-model="dataList.csnorWhseCt"
              label="联系人"
              placeholder="联系人"
            />
            <van-field
              readonly
              v-model="dataList.csnorWhseTel"
              label="联系方式"
              placeholder="联系方式"
            />
          </div>
          <div class="basic" v-show="item.title == '卸货方'">
            <van-field
              readonly
              v-model="dataList.csneeWhseAddr"
              label="企业"
              placeholder="企业"
              type="textarea"
              rows="1"
              autosize
            />
            <van-field
              readonly
              v-model="dataList.csneeWhseDist"
              label="地址"
              placeholder="地址"
              type="textarea"
              rows="1"
              autosize
            />
            <van-field
              readonly
              v-model="dataList.csneeWhseCt"
              label="联系人"
              placeholder="联系人"
            />
            <van-field
              readonly
              v-model="dataList.csneeWhseTel"
              label="联系方式"
              placeholder="联系方式"
            />
          </div>
          <div class="basic" v-show="item.title == '货物信息'">
            <van-field
              readonly
              v-model="dataList.goodsNm"
              label="货品名称"
              placeholder="货品名称"
            />
            <van-field
              readonly
              v-model="dataList.loadQty"
              label="装货数量(吨)"
              placeholder="装货数量(吨)"
            />
            <van-field
              readonly
              v-model="dataList.categroy"
              label="危险货物类别"
              placeholder="危险货物类别"
            />
            <van-field
              readonly
              v-model="dataList.un"
              label="联合国编码"
              placeholder="联合国编码"
            />
          </div>
        </van-collapse-item>
      </template>
    </van-collapse>
    <!-- 二维码  -->
    <div class="footer">
      <div class="text-center" @click="showOverlay">
        <div id="qrcode" align="center"></div>
        <!-- <h4 class="footer-h4" style="">
          使用官方APP扫描二维码核验详情
        </h4> -->
      </div>
      <van-overlay :show="show" @click="show = false">
        <div id="fullscreen" class="fullscreen hidden">
          <div class="text-center code-big">
            <div id="qrcodeBig" align="center"></div>
          </div>
        </div>
      </van-overlay>
    </div>
  </div>
</template>

<script>
import * as $http from "@/apis/rteplan.js";
import QRCode from "qrcodejs2";

export default {
  data() {
    return {
      activeName: [], //默认展开折叠面板
      activeTitle: [
        { title: "承运方", icon: "wap-home-o" },
        { title: "牵引车", icon: "logistics" },
        { title: "挂车", icon: "logistics" },
        { title: "罐体", icon: "apps-o" },
        { title: "驾驶员", icon: "manager-o" },
        { title: "押运员", icon: "manager-o" },
        { title: "委托方", icon: "apps-o" },
        { title: "装货方", icon: "apps-o" },
        { title: "卸货方", icon: "apps-o" },
        { title: "货物信息", icon: "apps-o" },
      ],
      dataList: {},
      show: false,
      qrCodeStr: null,
    };
  },
  created() {
    let pk = this.$route.query.rtePlanPk || "";
    // let pk = "4073263316421093";
    this.getInfo(pk);
    this.activeTitle.forEach((item) => {
      this.activeName.push(item.title);
    });
  },
  methods: {
    getInfo(pk) {
      let param = { id: pk };
      $http
        .getrtePlanInfo(param)
        .then((res) => {
          if (res.code == 0) {
            this.dataList = res.data;
            this.createdQRCode(pk);
          } else {
            this.$toast("载入电子运单数据出错！");
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    createdQRCode(pk) {
      if (pk) {
        let param = { rtePlanPk: pk };
        $http
          .createdQRCode(param)
          .then((res) => {
            if (res) {
              new QRCode(document.getElementById("qrcode"), {
                text: res,
                width: 150,
                height: 150,
                colorDark: "#000000",
                colorLight: "#ffffff",
                correctLevel: QRCode.CorrectLevel.H,
              });
              this.qrCodeStr = res;
              this.createdBigQRCode();
            }
          })
          .catch((error) => console.log(error));
      }
    },
    createdBigQRCode() {
      new QRCode(document.getElementById("qrcodeBig"), {
        text: this.qrCodeStr,
        width: 350,
        height: 350,
        colorDark: "#000000",
        colorLight: "#ffffff",
        correctLevel: QRCode.CorrectLevel.H,
      });
    },
    showOverlay() {
      this.show = true;
      // this.createdBigQRCode();
    },
  },
};
</script>

<style lang="scss" scoped>
.main-content {
  width: 100vw;
  // height: 100vh;
  background-color: #eeeeee;
}
.collapse {
  margin-bottom: 20px;
}
::v-deep .van-cell {
  color: #679de0 !important;
}
.rteplanInfo {
  margin-bottom: 20px !important;
}
.footer {
  padding: 0px 0 20px;
  .text-algin {
    margin: 0 auto;
    .footer-h4 {
      text-align: center;
      font-size: 18px;
      padding-bottom: 10px;
    }
    .footer-tips {
      text-align: center;
      font-size: 18px;
    }
  }
  .fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
  }

  .hidden {
    display: block !important;
    .code-big {
      position: fixed;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }
  }
}
</style>
