import * as $validateFn from "@/utils/validate";

const formRulesFilter = {};
const defaultTrigger = "onChange";

// 注意:  定义type 规则时 不用做非空验证
formRulesFilter.install = (Vue) => {
  // 验证手机号
  const valMobile = (value) => {
    if (
      $validateFn.isNoVal(value) ||
      (!$validateFn.isNoVal(value) && $validateFn.isMobile(value))
    ) {
      return true;
    }
    return false;
  };

  // 验证身份证号
  const valID = (value) => {
    if (
      $validateFn.isNoVal(value) ||
      (!$validateFn.isNoVal(value) && $validateFn.isID(value))
    ) {
      return true;
    }
    return false;
  };

  // 验证车牌号 License Plate Number
  const valLPN = (value) => {
    if (
      $validateFn.isNoVal(value) ||
      (!$validateFn.isNoVal(value) && $validateFn.isLPN(value))
    ) {
      return true;
    }
    return false;
  };

  // 验证车架号
  const valVIN = (value) => {
    if (
      $validateFn.isNoVal(value) ||
      (!$validateFn.isNoVal(value) && $validateFn.isVIN(value))
    ) {
      return true;
    }
    return false;
  };

  // 验证是否是整数
  const valIsInteger = (value) => {
    if (
      $validateFn.isNoVal(value) ||
      (!$validateFn.isNoVal(value) && /^\d+$/g.test(value))
    ) {
      return true;
    }
    return false;
  };

  /**
   * 参数 item
   * required true  必填项
   * maxLength  字符串的最大长度
   * min 和 max 必须同时给 min < max  type=number
   * type 手机号 mobile
   * 邮箱   email
   * 网址   url
   */
  Vue.prototype.$formRules = function (item) {
    const rules = [];
    if (item.required) {
      if (item.trigger) {
        rules.push({
          required: true,
          message: "必填项!",
          trigger: item.trigger,
        });
      } else {
        rules.push({
          required: true,
          message: "必填项!",
          trigger: defaultTrigger,
        });
      }
    }
    if (item.maxLength) {
      rules.push({
        min: 1,
        max: item.maxLength,
        message: "最多输入" + item.maxLength + "个字符!",
        trigger: defaultTrigger,
      });
    }
    if (item.min && item.max) {
      rules.push({
        min: item.min,
        max: item.max,
        message: "字符长度在" + item.min + "至" + item.max + "之间!",
        trigger: defaultTrigger,
      });
    }
    if (item.isInteger) {
      if (item.trigger) {
        rules.push({ validator: valIsInteger, trigger: item.trigger });
      } else {
        rules.push({ validator: valIsInteger, trigger: defaultTrigger });
      }
    }
    if (item.validator) {
      if (item.trigger) {
        rules.push({ validator: item.validator, trigger: item.trigger });
      } else {
        rules.push({ validator: item.validator, trigger: defaultTrigger });
      }
    }

    if (item.type) {
      const type = item.type;
      switch (type) {
        case "email": // 邮箱
          if (item.trigger) {
            rules.push({
              type: "email",
              message: "请输入正确的邮箱地址",
              trigger: item.trigger,
            });
          } else {
            rules.push({
              type: "email",
              message: "请输入正确的邮箱地址",
              trigger: defaultTrigger,
            });
          }
          break;
        case "date": // 日期
          rules.push({
            type: "date",
            message: "请选择日期",
            trigger: defaultTrigger,
          });
          break;
        case "array": // checkbox,至少选择一个
          rules.push({
            type: "array",
            message: "请至少选择一个",
            trigger: defaultTrigger,
          });
          break;
        case "mobile": // 手机号
          if (item.trigger) {
            rules.push({
              validator: valMobile,
              trigger: item.trigger,
              message: "请输入正确的手机号",
            });
          } else {
            rules.push({
              validator: valMobile,
              trigger: defaultTrigger,
              message: "请输入正确的手机号",
            });
          }
          break;
        case "uscCd": // 统一社会信用代码uscCd
          if (item.trigger) {
            rules.push({
              validator: valUscCd,
              trigger: item.trigger,
              message: "请输入正确的统一社会信用代码",
            });
          } else {
            rules.push({
              validator: valUscCd,
              trigger: defaultTrigger,
              message: "请输入正确的统一社会信用代码",
            });
          }
          break;
        case "ID": // 身份证
          if (item.trigger) {
            rules.push({
              validator: valID,
              trigger: item.trigger,
              message: "请输入正确的身份证号",
            });
          } else {
            rules.push({
              validator: valID,
              trigger: defaultTrigger,
              message: "请输入正确的身份证号",
            });
          }
          break;
        case "LPN": // 车牌号
          if (item.trigger) {
            rules.push({
              validator: valLPN,
              trigger: item.trigger,
              message: "请输入正确的车牌号",
            });
          } else {
            rules.push({
              validator: valLPN,
              trigger: defaultTrigger,
              message: "请输入正确的车牌号",
            });
          }
          break;
        case "VIN": // 车架号
          if (item.trigger) {
            rules.push({
              validator: valVIN,
              trigger: item.trigger,
              message: "请输入正确的车架号",
            });
          } else {
            rules.push({
              validator: valVIN,
              trigger: defaultTrigger,
              message: "请输入正确的车架号",
            });
          }
          break;
        default:
          break;
      }
    }
    return rules;
  };
};

export default formRulesFilter;
