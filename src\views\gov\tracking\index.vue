<!--
  @desc:轨迹跟踪
  @date:2023-08-15
-->
<template>
  <div style="height: 100%;display: flex;flex-direction: column;">
    <div class="search-box">
      <van-search v-model="searchValue" placeholder="请输入车牌号搜索"></van-search>
      <div class="search-res" v-if="carList.length">
        <van-cell v-for="(item, index) in carList" :key="index" :value="item.name" @click="selectVecHdl(item)" />
      </div>
      <van-tag type="primary" size="large" class="select-vec" v-if="selectVec">{{ selectVec }}</van-tag>
    </div>
    <div class="middle-box" id="map"></div>
    <div class="bottom-box">
      <van-tag type="primary" size="large" class="his-tag">历史轨迹</van-tag>
      <van-tabs type="card" color="#1989FA" v-model="selectHis">
        <van-tab v-for="item in hisArr" :key="item" :name="item" :title="`前${item}小时`"></van-tab>
      </van-tabs>
      <van-button plain hairline type="info" round class="info" @click="show = true">运单信息</van-button>
    </div>
    <van-popup
        v-model="show"
        closeable
        position="right"
        :style="{ width: '90%',height: '100%' }"
    >
      <iframe class="rtePlaninfo" v-if="cd" :src="`./rteplanInfo.html?cd=${cd}`"></iframe>
    </van-popup>
  </div>
</template>

<script>
import * as $http from "@/apis/tracking.js"
import { getRequestValueByParam } from "@/utils/common";
import store from "@/store";

const vecKey = 'DC_VEC_KEY'
const initLong = 121.66386
const initLat = 30.001186
export default {
  name: "tracking",
  data() {
    return {
      // search
      searchValue: '',
      carList: [],
      selectVec: localStorage.getItem(vecKey),
      // history
      hisArr: [1, 6, 12, 24],
      selectHis: 1,
      map: null,
      his: [],
      vecMarker: null,
      newHis: [],
      newHisOVerlay: null,
      show: false,
      cd: ''
    }
  },
  created() {
    let query = this.$route.query
    let token = query.token
    this.$store.dispatch("user/setToken", token);

    const vecNo = getRequestValueByParam('vecNo')
    if (vecNo) {
      this.selectVecHdl({
        name: decodeURI(vecNo)
      })
    }
  },
  mounted() {
    this.initMap()
    this.initWs()
  },
  watch: {
    searchValue() {
      if (this.searchValue.length >= 3) {
        this.getCarList()
      } else {
        this.carList = []
      }
    },
    selectHis: {
      handler() {
        this.getHis()
      },
      immediate: true
    }
  },
  methods: {
    initWs() {
      let token = store.getters.token
      const ws = new WebSocket("wss://zhys.dacyun.com/webSocket?token=" + token);
      ws.onopen = ()=> {
        sendServer()
        function sendServer() {
          ws.send('hello server! I am live.');
          setTimeout(() => {
            sendServer()
          }, 10 * 1000);
        }
        ws.onmessage = (event)=> {
          const data = event.data
          try {
            const d = JSON.parse(data)
            if (d.catNm === 'gps') {
              this.addHis(d.msg)
            }
          } catch(e) {}
        };
      }
    },
    // 获取车辆列表
    async getCarList() {
      let params = {
        vecNo: this.searchValue
      }
      const res = await $http.getVecFuzzyBw(params)

      if (res) {
        this.carList = res.data
      }
    },
    // 选中车辆
    selectVecHdl(v) {
      this.selectVec = v.name
      localStorage.setItem(vecKey, v.name)
      this.searchValue = ''
      this.getHis()
    },
    // 初始化地图
    initMap() {
      this.map = new BMap.Map("map", {
        enableMapClick: false
      });
      var point = new BMap.Point(initLong, initLat);

      this.map.centerAndZoom(point, 12);
      this.map.enableScrollWheelZoom();
    },
    // 获取历史数据
    async getHis() {
      if (!this.selectVec) {
        return
      }
      this.getNewRetplan()
      const format = 'YYYY-MM-DD HH:mm:ss'
      const start = dayjs().subtract(this.selectHis, 'h').format(format)
      const end = dayjs().format(format)

      let params = {
        vehicleNo: this.selectVec,
        startTime: start,
        endTime: end,
      }
      const res = await $http.getGpsHistory(params)
      if (res) {
        this.his = res.data
        this.setHis()
      }
      this.newHis = []
      this.initMark()
      this.startTrack()
    },
    setHis() {
      this.map.clearOverlays()
      const track = this.his.map(item=> `${item.lonBd},${item.latBd}`).join(';')
      let pointArray = [];
      const boundaries = [track];
      boundaries.forEach(item => {
        var ply = new BMap.Polyline(item, {
          strokeColor: "#0000ff",
          strokeOpacity: 1,
          strokeWeight: 3,
          strokeStyle: 'dashed'
        });
        this.map.addOverlay(ply);
        pointArray = pointArray.concat(ply.getPath());
      })
      this.map.setViewport(pointArray);
    },
    initMark() {
      const last = this.his.length ? this.his[this.his.length - 1] : null
      const long = last ? last.lonBd : initLong
      const lat = last ? last.latBd : initLong
      this.vecMarker = new BMap.Marker(
          new BMap.Point(long, lat),
          { icon: new BMap.Icon("/images/monitor-img/_icon_r.png", new BMap.Size(73, 73)) }
      );
      this.map.addOverlay(this.vecMarker);
      if (last) {
        this.addHis(last)
      }
    },
    async startTrack() {
      let params = {
        vecNo: this.selectVec,
        mm: 10,
      }
      await $http.getGpsTrace(params)
    },
    addHis(d) {
      if (d.vehicleNo !== this.selectVec) return
      this.newHis.push(d)

      const track = this.newHis.map(item=> `${item.lonBd},${item.latBd}`).join(';')
      this.newHisOVerlay = new BMap.Polyline(track, {
        strokeColor: "#1989FA",
        strokeOpacity: 1,
        strokeWeight: 3,
      });
      this.newHisOVerlay.name = 'newpl'
      this.map.addOverlay(this.newHisOVerlay);
      const pt = new BMap.Point(d.lonBd, d.latBd)
      this.map.panTo(pt)
      this.vecMarker.setPosition(pt);
      this.vecMarker.setRotation((d.direction - 90))
    },
    async getNewRetplan() {
      let params = {
        tracCd: this.selectVec,
      }
      const res = await $http.getCurrentRtePlan(params)
      if (res) {
        this.cd = res.data.cd
      }
    }
  }
};
</script>

<style scoped>
.search-box{
  height: 108px;
  border-bottom: 1px solid #ddd;
  position: relative;
}
.search-res{
  position: absolute;
  top: 55px;
  left: 0;
  width: 100%;
  max-height: 50vh;
  overflow: auto;
  background: #fff;
  border-bottom: 1px solid #ddd;
  z-index: 99;
}
.select-vec{
  position: fixed;
  top: 110px;
  left: 5px;
  z-index: 90;
}
.middle-box{
  flex: 1;
}
.bottom-box{
  position: relative;
}
.van-tabs__nav--card{
  margin: 0;
}
.van-tabs__wrap, .van-tabs__nav--card{
  height: 40px !important;
}
.his-tag{
  border-radius: 0px 10px 0 0;
  position: absolute;
  bottom :100%;
  left: 0;
}
.info{
  position: absolute;
  right: 10px;
  bottom: calc(100% + 10px);
}
.rtePlaninfo{
  width: 100%;
  height: 100%;
}
</style>