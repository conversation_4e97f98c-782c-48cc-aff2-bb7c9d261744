<!--
  ** date: 2023-8-14
  ** author: zhangxx
  ** desc: 运营月报
 -->
<template>
  <div class="center">
    <div class="time_header">
      <div class="time_box"
           @click="show=true">{{currentDate}}</div>
    </div>
    <van-popup v-model="show"
               position="bottom"
               :style="{ height: '50%' }">
      <van-datetime-picker v-model="currentDate1"
                           type="year-month"
                           title="选择年月日"
                           @confirm="confirm"
                           @cancel="show=false"
                           :max-date="maxDate"
                           :min-date="minDate" />
    </van-popup>

    <iframe width="100%"
            style="height:95.5vh"
            :src="pdfUrl"
            frameborder="0"></iframe>

  </div>
</template>

<script>
import * as $http from "@/apis/report";
import { Base64 } from "js-base64";
import { formatDate } from "@/utils/common";
export default {
  name: "",
  data () {
    return {
      currentDate: formatDate(new Date(), "yyyy-MM"),
      currentDate1: '',
      pdfList: [],
      loadingTask: '',
      pdfDoc: {},
      pdfUrl: '',
      scale: 1,
      show: false,
      minDate: new Date(2022, 11),
      maxDate: new Date(),
    };
  },
  created () {
    let query = this.$route.query
    let token = query.token
    this.$store.dispatch("user/setToken", token);
  },
  mounted () {
    this.getPaf(this.currentDate)
  },

  computed: {

  },
  methods: {
    getPaf (tiem) {
      let obj = {
        "groupOp": "AND",
        "rules": [{
          "field": 'mt',
          "op": "cn",
          "data": tiem
        }]
      }
      let data = {
        filters: JSON.stringify(obj),
        page: 1,
        limit: 20
      }
      $http.getRptMonthly(data).then(res => {
        let _this = this
        _this.pdfList = res.page.list
        if (_this.pdfList.length > 0) {
          _this.pdfUrl = _this.pdfList[0].downUrl; //直接修改iframe的src
          let src = _this.pdfList[0].downUrl;
          this.pdfUrl = "https://fileview.dacyun.com/preview/onlinePreview?url=" +
            encodeURIComponent(Base64.encode(src)) + '&officePreviewType=pdf'
        }
      })
    },

    confirm (item) {
      this.currentDate = formatDate(item, "yyyy-MM")
      this.show = false
      this.getPaf(this.currentDate)
    },
  },
  destroyed () {

  },
};
</script>

<style lang="scss" scoped >
.time_header {
  position: sticky;
  top: 0;
  height: 70px;
  background: linear-gradient(rgb(42, 83, 176), rgb(31, 61, 129));
  padding: 5px 0 0 5%;
  // align-items: center;
  z-index: 10;
}
.time_box {
  width: 90%;
  height: 60px;
  background: #fff;
  color: #333;
  font-size: 42px;
  line-height: 60px;
  border-radius: 2px;
}
</style>