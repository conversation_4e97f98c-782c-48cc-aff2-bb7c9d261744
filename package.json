{"name": "dcys-h5", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "build:stag": "vue-cli-service build --mode stag"}, "dependencies": {"amfe-flexible": "^2.2.1", "core-js": "^3.8.3", "dayjs": "^1.11.9", "good-storage": "^1.1.1", "normalize.css": "^8.0.1", "postcss-pxtorem": "^6.0.0", "qrcodejs2": "0.0.2", "qs": "^6.11.2", "vant": "^2.12.54", "vue": "^2.6.14", "vue-router": "^3.5.1", "vuex": "^3.6.2"}, "devDependencies": {"@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "sass": "^1.32.7", "sass-loader": "^12.0.0", "vue-template-compiler": "^2.6.14"}}