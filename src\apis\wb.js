import request from "@/utils/request";


// 图片上传
export function upload(params) {
    return request({
        url: "/whjk-cp/sys/oss/upload/multi",
        method: "post",
        data: params,
        headers: {
            "Content-Type": "multipart/form-data",
        },
    });
}


//上传磅单
export function uploadArgmtUrl(params) {
    return request({
        url: "/whjk-cp/argmwt/uploadArgmtUrl",
        method: "post",
        data: params,
        headers: {
            "Content-type": "application/json;charset=UTF-8",
        },
    });
}


// 磅单详情
export function getArgmtWtDetail(pk, token) {
    return request({
        url: "/whjk-cp/argmtaftcheck/info/" + pk,
        method: "get",
        // params: params,
        token: token,
        headers: {
            "Content-type": "application/json;charset=UTF-8",
        },
    });
}


// 运单详情
export function getRteDetail(pk, token) {
    return request({
        url: "/whjk-cp/rtePlan/detail/" + pk,
        method: "get",
        // params: params,
        token: token,
        headers: {
            "Content-type": "application/json;charset=UTF-8",
        },
    });
}

