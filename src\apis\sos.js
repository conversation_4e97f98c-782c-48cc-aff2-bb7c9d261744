import request from "@/utils/request";

/**
 *
 * @param {Object} params {'wechatId': wechatId,'bindType': 1}
 * @returns
 */
//获取未完成电子运单信息
export function getLast4Wechat(params) {
  return request({
    url: "/zh/whjk-gov/rtePlan/getLast4Wechat?vno=" + params,
    method: "get",
  });
}
/**
 *
 * @param {Object} params {'wechatId': wechatId,'bindType': 1}
 * @returns
 */
// 获取gps信息
export function findGpsByVecNo(params) {
  return request({
    url: "/zh/whjk-gov/gps/findGpsByVecNo?vecNo=" + params,
    method: "get",
  });
}
/**
 *
 * @param {Object} params {'wechatId': wechatId,'bindType': 1}
 * @returns
 */
//查看牵引车详情
export function getVecInfo(params) {
  return request({
    url: "/zh/whjk-gov/vec/itm/" + params + "",
    method: "get",
  });
}
/**
 *
 * @param {Object} params {'wechatId': wechatId,'bindType': 1}
 * @returns
 */
//查看人员详情
export function getPersInfo(params) {
  return request({
    url: "/zh/whjk-gov/pers/itm/" + params,
    method: "get",
  });
}
/**
 *
 * @param {Object} params {'wechatId': wechatId,'bindType': 1}
 * @returns
 */
//查看企业详情
export function getEntpInfo(params) {
  return request({
    url: "/zh/whjk-gov/entp/itm/" + params,
    method: "get",
  });
}
