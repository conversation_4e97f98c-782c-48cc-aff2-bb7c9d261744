import axios from "axios";
import store from "@/store";
import router from "@/router";
import { Toast, Notify } from "vant";
// create an axios instance
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API_URL,
  // withCredentials: true, // send cookies when cross-domain requests
  timeout: 10000, // request timeout
});

// request拦截器 request interceptor
service.interceptors.request.use(
  (config) => {
    // 不传递默认开启loading
    if (!config.hideloading) {
      // loading
      Toast.loading({
        forbidClick: true,
      });
    }
    if (store.getters.token) {
      config.headers["token"] = store.getters.token;
    }
    return config;
  },
  (error) => {
    // do something with request error
    console.log(error); // for debug
    return Promise.reject(error);
  }
);
// respone拦截器
service.interceptors.response.use(
  (response) => {
    Toast.clear();
    const res = response.data;
    const status = response.status;
    const code = res?.code || 200;
    // 这里注意修改成你访问的服务端接口规则
    // 登录超时,重新登录
    if (code === 401) {
      store.dispatch("user/fedLogOut").then(() => {
        let r = router.currentRoute;
        let type = r.meta?.type;
        let path = "";
        switch (type) {
          case 1:
            path = "/entpLogin";
            break;
          case 3:
            path = "/personLogin";
            break;
          default:
            path = "/login";
            break;
        }
        router.push({
          path: path,
          query: { redirect: router.currentRoute.fullPath },
        });
        // location.reload();
      });
    }
    if (status !== 200) {
      Notify({ type: "danger", message: res.msg });
      return Promise.reject(new Error(res.msg));
    } else if (code === 500) {
      Notify({ type: "danger", message: res.msg });
    }
    return res;
  },
  (error) => {
    Toast.clear();
    console.log("err" + error); // for debug
    return Promise.reject(error);
  }
);

export default service;
