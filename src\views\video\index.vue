<!--
  ** /video/video.html?%7B"hls"%3A"https%3A%2F%2Ftranscode1.dacyun.com%2Fhls%2F10ee8b24-5a6c-11e8-a8aa-00163e10b7d7.m3u8","snapUrl"%3A"https%3A%2F%2Fipc-pic.oss-cn-hangzhou.aliyuncs.com%2Fscreenshot%2Fipc%2F10ee8b24-5a6c-11e8-a8aa-00163e10b7d7%2F20230823090811.jpg"%7D
  ** date: 2023-8-14
  ** desc: 浙政钉视频播放
 -->
<template>
  <div class="page-content">
    <van-empty v-if="showErr" image="error" :description="errText" />
    <div id="video-node" class="video-wrap"></div>
  </div>
</template>

<script>
export default {
  name: "cpVideo",
  data() {
    return {
      errText:'很抱歉，当前视频无法播放！',
      showErr:false,
      flvPlayer: null,
      timer: null,
      videoData:{}
    };
  },
  mounted() {
    var urlParams = location.href.split('?')[1];
    if(!urlParams || urlParams.length===0){
      this.showErr = true
    }else{
      this.videoData = JSON.parse(decodeURIComponent(urlParams)) || {};
      if(!this.videoData || (!this.videoData.flv && !this.videoData.hls)){
        this.showErr = true
      }else{
        this.initVideoPlayer();
      }
    }
  },
  methods: {
    initVideoPlayer () {
      // this.videoData = {
      //   flv: "https://video-stream-1.dacyun.com/sms/34020000002020000001/flv/hls/34020000001110000021_34020000001320021001.flv",
      //   hls: "https://video-stream-1.dacyun.com/sms/34020000002020000002/hls/33021100071327000009_33021100071327000009/33021100071327000009_33021100071327000009_live.m3u8",
      //   snapUrl: "https://video-stream-1.dacyun.com/sms/34020000002020000002/snap/34020000001110000002/34020000001320002001.jpg",
      //   entpName: "宁波大地化工环保有限公司",
      //   channelName: "1号卸货场",
      // };
      var flag = this.isMobile();
      var url = flag ? this.videoData.hls : ( this.videoData.flv || this.videoData.hls);
      if (url) {
        if(url.indexOf('.flv')===-1 && url.indexOf('.m3u8')===-1){
          this.errText = '很抱歉，视频播放地址有误，无法播放视频！'
          this.showErr = true
        }
        this.flvPlayer = new Aliplayer({
          "cover": this.videoData.snapUrl,
          "id": "video-node",
          // "source": "https://video-stream-1.dacyun.com/sms/34020000002000000001/hls/33021100071327000001_33021100071327000001/33021100071327000001_33021100071327000001_live.m3u8",
          // "source": "https://video-stream-1.dacyun.com/sms/34020000002000000001/flv/hls/33021100071327000001_33021100071327000001.flv",
          "source": url,
          "width": "100%",
          "height": "100%",
          "autoplay": true,
          "isLive": true,
          "rePlay": false,
          "playsinline": true,
          "preload": true,
          "controlBarVisibility": "hover",
          "useH5Prism": true
        }, function (player) {
          console.log("The player is created");
          // setTimeout(function () {
          //   player.play();
          //   player.setRotate(90);
          //   // player.setImage("horizon")
          // }, 1000)

        });
        this.flvPlayer && this.flvPlayer.on('play',function(){
          this.timer && clearTimeout(this.timer)
          this.timer = setTimeout(function () {
            this.flvPlayer.pause();
          },600000);
        })
      } else {
        this.errText = this.videoData.flv || this.videoData.hls || ''
        this.showErr = true
      }
    },
    isMobile () {
      if (window.navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i)) {
        return true; // 移动端
      } else {
        return false; // PC端
      }
    },
    flv_destroy () {
      this.flvPlayer.pause();
      this.flvPlayer.dispose();
      this.flvPlayer = null;
      this.timer && clearTimeout(this.timer)
    }
  },
  destroyed() {
    this.flv_destroy()
  },
};
</script>

<style lang="scss" scoped>
.page-content {
  height: 100vh;
  width: 100vw;
  overflow-y: auto;
}
.video-wrap{
  height: 100%;
  width: 100%;
  overflow: hidden;
}
</style>