<!--
  ** date: 2023-8-14
  ** author: zhangxx
  ** desc: 运营日报
  /daily-report/index.html?dt=2023-08-22
 -->
<template>
  <div class="page-content">
    <div class="title_box">
      <div class="title_bg">
        <div class="title_text">变量数据</div>
      </div>
      <div class="header">
        <div class="header_text">运营报告</div>

        <div class="picker" @click="show = true">{{ date }}</div>
      </div>
      <van-calendar v-model="show" @confirm="onConfirm" title="选择时间" :min-date="minDate" :max-date="maxDate" />

    </div>
    <!-- <div class="heard1">
    </div>
    <div class="heard1_bg"></div>
    <div class="heard1_text">车辆违章统计</div> -->
    <div class="daily_conent">
      <van-row>
        <van-col span="8">
          <div class="daily_num">{{ dailyInfo.inZh }}</div>
          <div class="daily_name">进镇海</div>
        </van-col>
        <van-col span="8">
          <div class="daily_num">{{ dailyInfo.outZh }}</div>
          <div class="daily_name">出镇海</div>
        </van-col>
        <van-col span="8">
          <div class="daily_num">{{ dailyInfo.passZh }}</div>
          <div class="daily_name">过境车</div>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="12">
          <div class="daily_num">{{ dailyInfo.loadQty }}</div>
          <div class="daily_name">装货吨数</div>
        </van-col>
        <van-col span="12">
          <div class="daily_num">{{ dailyInfo.loadCatCnt }}</div>
          <div class="daily_name">装货种类</div>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="12">
          <div class="daily_num">{{ dailyInfo.unLoadQty }}</div>
          <div class="daily_name">卸货吨数</div>
        </van-col>
        <van-col span="12">
          <div class="daily_num">{{ dailyInfo.unLoadCatCnt }}</div>
          <div class="daily_name">卸货种类</div>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="12">
          <div class="daily_num">{{ dailyInfo.heavyRegCnt }}</div>
          <div class="daily_name">重车登记</div>
        </van-col>
        <van-col span="12">
          <div class="daily_num">{{ dailyInfo.emptyRegCnt }}</div>
          <div class="daily_name">空车登记</div>
        </van-col>
      </van-row>
    </div>
    <div class="title_box">
      <div class="title_bg">
        <div class="title_text">车辆违章统计</div>
      </div>
    </div>
    <table class="biaoge" v-if="dailyInfo.alarm">
      <tr v-for="(item, index) in Object.keys(dailyInfo.alarm)" :key="index">
        <td colspan="2" class="table_name"> {{ item }}</td>
        <td colspan="2">{{ dailyInfo.alarm[item] }}</td>
      </tr>
    </table>
  </div>
</template>

<script>
import * as $http from "@/apis/report";
import { formatDate } from "@/utils/common";
export default {
  name: "",
  data() {
    return {
      date: '',
      show: false,
      dailyInfo: {},
      minDate: new Date(2010, 1, 1),
      maxDate: new Date(2100, 1, 31),
    };
  },
  created() {
    let query = this.$route.query
    let token = query.token
    this.$store.dispatch("user/setToken", token);
  },
  mounted() {
    let diyDate = new Date()
    this.date = formatDate(new Date(diyDate.setDate(diyDate.getDate() - 1)), "yyyy-MM-dd")
    this.getdailyInfo(this.date)
  },
  methods: {
    onConfirm(date) {
      this.show = false;
      this.date = formatDate(date, "yyyy-MM-dd");
      this.getdailyInfo(this.date)
    },
    getdailyInfo(date) {
      $http.getAlarmSmsDay(date).then(res => {
        if (res.code === 0) {
          this.dailyInfo = res.data
        }
      })
    }
  },
  destroyed() {

  },
};
</script>

<style lang="scss" scoped>
.page-content {
  height: 100vh;
  width: 100vw;
  overflow-y: auto;
  background: linear-gradient(to bottom, #546cd6, #5b69e0);
}

.title_box {
  width: 100%;
  height: 15%;
}

.title_bg {
  float: left;
  display: block;
  position: relative;
  margin-top: 5%;
  width: 55%;
  height: 12vw;
  background: url('~static/images/report/title_bg.png') no-repeat center center;
  background-size: 100% 100%;
}

.title_text {
  color: #fff;
  font-size: 6vw;
  margin-left: 7%;
  z-index: 1;
  padding-top: 5%;
}

.header {
  float: right;
  width: 30%;
  margin-right: 15px;
  height: 70%;
  color: #fff;
  font-size: 5vw;
  padding: 15px;
  text-align: center;
  background: url('~static/images/report/day_bg.png') no-repeat center center;
  background-size: 100% 100%;
}

.picker {
  margin-top: 30px;
}

.header_text {
  margin-top: 15px;
}

.daily_conent {
  width: 100%;
  text-align: center;
  color: #efefef;

  .daily_num {
    font-size: 6vw;
    background: linear-gradient(to bottom, #ffd04b, #e3a009);
    font-weight: 600;
    -webkit-background-clip: text;
    color: transparent;
  }

  .daily_name {
    font-size: 4vw;
  }

  .van-row {
    margin-top: 6%;
  }
}

table {
  border-collapse: collapse;
  width: 90%;
  margin-left: 5%;
  top: 65%;
  height: 25%;
  text-align: center;
  font-weight: 600;
  color: #fff;
}

.table_name {
  background: #3c53d1;
}

tr,
td {
  border: 1px solid #fff;
}

td {
  width: 80px;
  height: 30px;
}</style>