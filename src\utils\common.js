/**
 * desc：公共方法库
 * author:gsj
 * date:2023-07-13
 */
export function getPageTitle(pageTitle) {
  if (pageTitle) {
    return `${pageTitle}`;
  }
  // return `${process.env.VUE_APP_TITLE}`;
  return "";
}

/**
 * 获取url的所有参数值对象
 * @param {string|null} urlString
 * @returns object
 */
export function getUrlParams(urlString) {
  let url = urlString || window.location.href;
  // 通过 ? 分割获取后面的参数字符串
  let urlStr = url.split("?")[1];
  // 创建空对象存储参数
  let obj = {};
  // 再通过 & 将每一个参数单独分割出来
  let paramsArr = urlStr.split("&");
  for (let i = 0, len = paramsArr.length; i < len; i++) {
    // 再通过 = 将每一个参数分割为 key:value 的形式
    let arr = paramsArr[i].split("=");
    obj[arr[0]] = arr[1];
  }
  return obj;
}

/**
 * 用于获取URL中"?"符后的参数值
 * @param {string} name
 * @returns
 */
// export function getUrlParam(name) {
//   let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
//   let r = window.location.search.slice(1).match(reg); //匹配目标参数

//   if (r != null) return unescape(r[2]);
//   return null; //返回参数值
// }
export function getUrlParam(name) {
  return (
    decodeURIComponent(
      (new RegExp("[?|&]" + name + "=" + "([^&;]+?)(&|#|;|$)").exec(
        location.href
      ) || [, ""])[1].replace(/\+/g, "%20")
    ) || null
  );
}

/**
 * 用于删除URL中的参数，返回删除后的url值
 * @param {string} paramKey
 * @returns {string}
 */
export function delUrlParam(paramKey) {
  let url = window.location.href; //页面url
  let urlParam = window.location.search.substr(1); //页面参数
  let beforeUrl = url.substr(0, url.indexOf("?")); //页面主地址（参数之前地址）
  let nextUrl = "";

  let arr = new Array();
  if (urlParam != "") {
    let urlParamArr = urlParam.split("&"); //将参数按照&符分成数组
    for (let i = 0; i < urlParamArr.length; i++) {
      let paramArr = urlParamArr[i].split("="); //将参数键，值拆开
      //如果键雨要删除的不一致，则加入到参数中
      if (paramArr[0] != paramKey) {
        arr.push(urlParamArr[i]);
      }
    }
  }
  if (arr.length > 0) {
    nextUrl = "?" + arr.join("&");
  }
  url = beforeUrl + nextUrl;
  if (!url) {
    // url中没有参数时需要将页面地址重新拿到
    url = window.location.href;
  }
  return url;
}

// object转form data
export function toFormData(obj) {
  const data = new FormData();
  for (const key in obj) {
    data.append(key, obj[key]);
  }
  return data;
}

/**
 * 树形数据转换
 * @param {*} data
 * @param {*} id
 * @param {*} pid
 */
export function treeDataTranslate(data, id = "id", pid = "parentId") {
  let res = [];
  let temp = {};
  for (let i = 0; i < data.length; i++) {
    temp[data[i][id]] = data[i];
  }
  for (let k = 0; k < data.length; k++) {
    if (temp[data[k][pid]] && data[k][id] !== data[k][pid]) {
      if (!temp[data[k][pid]]["children"]) {
        temp[data[k][pid]]["children"] = [];
      }
      if (!temp[data[k][pid]]["_level"]) {
        temp[data[k][pid]]["_level"] = 1;
      }
      data[k]["_level"] = temp[data[k][pid]]._level + 1;
      temp[data[k][pid]]["children"].push(data[k]);
    } else {
      res.push(data[k]);
    }
  }
  res.forEach((value, index, array) => {
    if (value.children === undefined) {
      value.children = [];
    }
  });
  return res;
}

/**
 * 查询树形数组中某个元素的路径
 * @param data
 * @param id
 * @param indexArray
 * @returns {*}
 */
export function findIndexArray(data, id, indexArray) {
  let arr = Array.from(indexArray);
  for (let i = 0, len = data.length; i < len; i++) {
    arr.push(data[i]);
    if (data[i].menuId === id) {
      return arr;
    }
    let children = data[i].list;
    if (children && children.length) {
      let result = findIndexArray(children, id, arr);
      if (result) return result;
    }
    arr.pop();
  }
  return false;
}

/**
 * 判断两个对象是否一致
 * @param {Object} o1
 * @param {Object} o2
 * @return {boolean}
 */
export function isEquall(o1, o2) {
  let props1 = Object.getOwnPropertyNames(o1);
  let props2 = Object.getOwnPropertyNames(o2);
  if (props1.length !== props2.length) {
    return false;
  }
  for (let i = 0, max = props1.length; i < max; i++) {
    let propName = props1[i];
    if (o1[propName] !== o2[propName]) {
      return false;
    }
  }
  return true;
}

/**
 * 将base64中的数据部分转换为file
 * 例：data:image/jpeg;base64,/9j/4AA
 * @param fileName 文件名
 * @param fileType 文件类型
 * @param dataUrl 组件获取base64原始数据；格式如上
 * @returns {*}
 */
export function dataURLtoFile(fileName, fileType, dataUrl) {
  let arr = dataUrl.split(",");
  let bstr = atob(arr[1]);
  let n = bstr.length;
  let u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new File([u8arr], fileName, {
    type: fileType,
  });
}

/**
 * 下载本地base64编码图片到本地，参数为base64编码，移动端不可用，必须先上传到服务器才可下载
 * @param {string} dataURL
 */
export function load64Img(dataURL, filename) {
  let blob = this.base64ToBlob(dataURL);
  let evt = document.createEvent("HTMLEvents");
  let aLink = document.createElement("a"); //创建a标签
  evt.initEvent("click", true, true);
  aLink.download = filename || "标题.jpg";
  aLink.href = URL.createObjectURL(blob);
  aLink.click(); //按下a标签
}

/**
 * 将图片Base64 转成文件,注意：filename这个参数要给出后缀名,后缀名可以是png，否则上传到阿里云返回的文件路径是没有后缀的
 * @param {*} dataUrl
 * @param {*} filename
 * @returns
 */
export function base64toFile(dataUrl, filename) {
  let arr = dataUrl.split(","),
    mime = arr[0].match(/:(.*?);/)[1],
    bstr = atob(arr[1]),
    n = bstr.length,
    u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new File([u8arr], filename, { type: mime });
}

/**
 * 转换方法,base64转Blob,,参数是base64编码
 * @param {*} code
 * @returns
 */
export function base64ToBlob(code) {
  let parts = code.split(";base64,");
  let contentType = parts[0].split(":")[1];
  let raw = window.atob(parts[1]);
  let rawLength = raw.length;
  let uInt8Array = new Uint8Array(rawLength);
  for (let i = 0; i < rawLength; ++i) {
    uInt8Array[i] = raw.charCodeAt(i);
  }
  return new Blob([uInt8Array], { type: contentType });
}

/*
  将网络url图片链接转换成base64，主要用于html2canvas在微信公众号无法解决跨域的问题
  参数是图片的URL地址，通过promise对象转出,
  分两步，第1步，创造一个Image对象，第2步，将Image对象绘制成canvas，再将canvas转换成base64的img流。
  使用例子：this.until.getBase64Image(this.form.imgUrl).then(res=>{
              this.ava3=res
            })
   */
export function getBase64Image(url) {
  let this_ = this;
  return new Promise((resolve) => {
    let image = new Image();
    image.src = url + "?v=" + Math.random(); // 处理缓存
    image.crossOrigin = "*"; // 支持跨域图片
    image.onload = function () {
      let base64 = this_.drawBase64Image(image);
      resolve(base64);
    };
  });
}

/**
 * 将Image对象绘制成canvas，再将canvas转换成base64的img流。
 */
export function drawBase64Image(img) {
  let canvas = document.createElement("canvas");
  canvas.width = img.width;
  canvas.height = img.height;
  let ctx = canvas.getContext("2d");
  ctx.drawImage(img, 0, 0, img.width, img.height);
  let dataURL = canvas.toDataURL("image/png");
  return dataURL;
}

export function getUserAgentType() {
  let ua = navigator.userAgent.toLowerCase();
  console.log(navigator.userAgent);
  if (ua.match(/ghapp_ios_v2/i) == "ghapp_ios_v2") {
    return "ios_v2";
  } else if (ua.match(/ghapp_android_v2/i) == "ghapp_android_v2") {
    return "android_v2";
  } else if (ua.match(/app_ios/i) == "app_ios") {
    return "ios";
  } else if (ua.match(/app_android/i) == "app_android") {
    return "android";
  } else if (ua.match(/MicroMessenger/i) == "micromessenger") {
    return "wx";
  } else if (ua.match(/AlipayClient/i) == "alipayclient") {
    return "alipay";
  } else {
    return "page";
  }
}

export function getDomain() {
  return window.location.host;
}

export function formatDate(time, cFormat) {
  if (arguments.length === 0) {
    return null;
  }
  if (!time) {
    return "";
  }

  let fmt = cFormat || "yyyy-MM-dd HH:mm:ss";

  let date;
  if (typeof time === "object") {
    date = time;
  } else if (typeof time === "string") {
    date = new Date(time);
  } else {
    date = new Date(parseInt(time));
  }

  let o = {
    "M+": date.getMonth() + 1, //月份
    "d+": date.getDate(), //日
    "h+": date.getHours() % 12 == 0 ? 12 : date.getHours() % 12, //小时
    "H+": date.getHours(), //小时
    "m+": date.getMinutes(), //分
    "s+": date.getSeconds(), //秒
    "q+": Math.floor((date.getMonth() + 3) / 3), //季度
    S: date.getMilliseconds(), //毫秒
  };
  let week = {
    0: "\u65e5",
    1: "\u4e00",
    2: "\u4e8c",
    3: "\u4e09",
    4: "\u56db",
    5: "\u4e94",
    6: "\u516d",
  };
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
  }
  if (/(E+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, (RegExp.$1.length > 1 ? (RegExp.$1.length > 2 ? "\u661f\u671f" : "\u5468") : "") + week[date.getDay() + ""]);
  }
  for (let k in o) {
    if (new RegExp("(" + k + ")").test(fmt)) {
      fmt = fmt.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length));
    }
  }
  return fmt;
}

/**
 * //用于获取URL中"?"符后的参数值
 */
export function getRequestValueByParam(param) {
  var url = window.location.search;
  var res = "";
  let strs;
  if (url.indexOf("?") != -1) {
    var str = url.substr(1);
    strs = str.split("&");
    for (var i = 0; i < strs.length; i++) {
      if (strs[i].split("=")[0] == param)
        res = strs[i].split("=")[1];
    }
  }
  return res;
}

// 用于删除URL中的参数
export function delParam(paramKey) {
  var url = window.location.href;    //页面url
  var urlParam = window.location.search.substr(1);   //页面参数
  var beforeUrl = url.substr(0, url.indexOf("?"));   //页面主地址（参数之前地址）
  var nextUrl = "";

  var arr = new Array();
  if (urlParam != "") {
    var urlParamArr = urlParam.split("&"); //将参数按照&符分成数组
    for (var i = 0; i < urlParamArr.length; i++) {
      var paramArr = urlParamArr[i].split("="); //将参数键，值拆开
      //如果键雨要删除的不一致，则加入到参数中
      if (paramArr[0] != paramKey) {
        arr.push(urlParamArr[i]);
      }
    }
  }
  if (arr.length > 0) {
    nextUrl = "?" + arr.join("&");
  }
  url = beforeUrl + nextUrl;
  if (!url) {
    // url中没有参数时需要将页面地址重新拿到
    url = window.location.href
  }
  return url;
}