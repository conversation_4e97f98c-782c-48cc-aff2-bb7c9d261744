<template>
  <div class="main-content">
    <van-sticky class="clearfix">
      <van-pagination
        v-model="pagination.page"
        :page-count="pagination.total"
        mode="simple"
        prev-text
        next-text
        @change="changePage()"
      ></van-pagination>
    </van-sticky>
    <div class="info-card-wape" v-for="(item, index) in dataList" :key="index">
      <van-row class="info-card-content">
        <!-- <h2>
          <span>{{ item.catNmCn }}</span>
        </h2> -->
        <van-col span="24">
          <span class="detail-label">违章时间</span>
          <span class="detail-value">{{ item.alarmTime }}</span>
        </van-col>
        <van-col span="24">
          <span class="detail-label">报警企业</span>
          <span class="detail-value">{{ item.entpNm }}</span>
        </van-col>
        <van-col span="24">
          <span class="detail-label">违章地点</span>
          <span class="detail-value">{{ item.alarmLocation }}</span>
        </van-col>
        <van-col span="24">
          <span class="detail-label">违章类型</span>
          <span class="detail-value">{{ item.catNmCn }}</span>
        </van-col>
        <van-col span="24" class="fix-height">
          <span class="detail-label">报警详情</span>
          <span class="detail-value">{{ item.descr }}</span>
        </van-col>
      </van-row>
    </div>
  </div>
</template>

<script>
import * as $http from "@/apis/violation";
import { getRequestValueByParam } from "@/utils/common";

export default {
  data() {
    return {
      dataList: [],
      pagination: {
        page: 1,
        limit: 10,
        total: 0,
      },
    };
  },
  created() {
    let v = getRequestValueByParam("v");
    let token = getRequestValueByParam("token");
    if (v) {
      this.init(token, vecNo);
    } else {
      this.$toast("对不起，车牌号不存在，无法查看违章记录详情！");
    }
  },
  methods: {
    init(token, vecNo) {
      let param = {
        tracNo: vecNo,
        token: token,
        notCatCd: "2550.160.180.150",
        page: this.pagination.page,
        limit: this.pagination.limit,
        days: 30,
      };
      $http
        .getAlarmList(param)
        .then((res) => {
          if (res.code == 0) {
            this.dataList = res.data.list;
            this.pagination.total = res.data.totalPage;
            this.pagination.page = res.data.pageNumber;
          } else {
            this.dataList = [];
            if (res.msg) {
              alert(res.msg);
            }
          }
        })
        .catch((error) => console.log(error));
    },
    changePage() {
      this.init();
    },
  },
};
</script>

<style lang="scss" scoped>
.main-content {
  width: 100vw;
  // height: 100vh;
  background-color: #fff;
}
.info-card-wape {
  margin: 0 auto;
  width: 95%;
  margin-bottom: 20px;
  border: 2px solid #e3e3e3;
  overflow: hidden;
  border-radius: 10px;
  box-shadow: 3px 5px 5px #dedede;
  position: relative;
  background-color: #fff;
  overflow-y: auto;

  .info-card-content {
    padding: 5px 16px 5px;
    line-height: 32px;
    // font-size: 16px !important;

    .detail-label {
      color: #0556ea;
      font-weight: bold;
    }
    .detail-value {
      padding-left: 15px;
    }

    .fix-height {
      max-height: 150px;
      // overflow-y: auto;
    }
  }
}

.info-card-content ::-webkit-scrollbar-track-piece {
  background-color: #ebeef7;
}

.info-card-content ::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.info-card-content ::-webkit-scrollbar-thumb {
  background: #e3e5eb;
  border-radius: 20px;
}

.clearfix {
  margin-bottom: 20px;
  ::v-deep .van-pagination {
    background-color: #fff;
  }
}

.clearfix:before,
.clearfix:after {
  content: "";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}
</style>
