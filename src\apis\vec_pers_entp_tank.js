import request from "@/utils/request";

/**
 * personInfo
 * @data {String} data
 * @returns
 */
//查看人员详情
export function getPersInfo(data) {
  return request({
    url: "/zjdc-wechat/pers/itm/" + data,
    method: "get",
    data: data,
    // headers:{
    //   "token":'dcp-sid-532d97a5-9c64-4755-8a98-5069a7142ab3'
    // }
  });
}

/**
 * vecInfo
 * @data {String} data
 * @returns
 */
//查看牵引车详情
export function getVecInfo(data) {
  return request({
    url: "/zjdc-wechat/vec/itm/" + data,
    method: "get",
    data: data,
  });
}

/**
 * entpInfo
 * @data {String} data
 * @returns
 */
//查看企业详情
export function getEntpInfo(data) {
  return request({
    url: "/zjdc-wechat/entp/itm/" + data,
    method: "get",
    data: data,
  });
}

/**
 * tankInfo
 * @data {String} data
 * @returns
 */
//查看罐体详情
export function getTankInfo(data) {
  return request({
    url: "/whjk-entp/tank/itm/" + data,
    // url: "/zjdc-wechat/tank/itm/" + data,
    method: "get",
    data: data,
    headers: {
      //   "token":'dcp-sid-532d97a5-9c64-4755-8a98-5069a7142ab3'
      areaId: "330211",
    },
  });
}
/**
 * tankInfo
 * @data {String} data
 * @returns
 */
//货物详情
export function getGoodsInfo(pk) {
  return request({
    url: "/zh/whjk-gov/chem/info/" + pk,
    // url: "/zjdc-wechat/tank/itm/" + data,
    method: "get",
  });
}