import request from "@/utils/request";

// 报警详情
export function selectAlarmByCd(cd) {
  return request({
    url: "/whjk-entp/alarm/selectAlarmByCd?cd=" + cd,
    method: "get",
    // params: params,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 发送短信验证码
export function sendsmsByAlarmPk(mob) {
  return request({
    url: "/whjk-entp/alarm/sendsmsByAlarmPk?mob=" + mob,
    method: "get",
    // params: params,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

// 验证码确认
export function checkCode(mob, codeVal) {
  return request({
    url: "/whjk-entp/alarm/checkCode",
    method: "get",
    params: { mob, codeVal },
  });
}

// 举证上传
export function alarmReplay(cd, replyRemark, replyUrl) {
  return request({
    url: "/whjk-entp/alarm/replay",
    method: "get",
    params: {
      cd,
      replyRemark,
      replyUrl,
    },
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
