/**
 * 注意：路由中auth:true 是说明当前页面是需要有权限才能查看的,一般游客用户是不能进入查看
 * type:1是代表企业用户，type:3代表驾押人员
 */
import Vue from "vue";
import VueRouter from "vue-router";
import entpRoutes from "./entpRoutes";
import govRoutes from "./govRoutes";
import dirverRoutes from "./dirverRoutes";
import ckRoutes from "./ckRoutes";
import cpRoutes from "./cpRoutes";
Vue.use(VueRouter);
// defaultRoutes 是 免登路由，无需授权
const defaultRoutes = [
  {
    path: "/",
    redirect: "/home",
  },
  {
    path: "/home",
    name: "home",
    component: () =>
      import(/* webpackChunkName: "home" */ "@/views/home/<USER>"),
    meta: { title: "主页", auth: false },
  },
  {
    path: "/login",
    name: "login",
    component: () =>
      import(/* webpackChunkName: "login" */ "@/page/login/index.vue"),
    meta: { title: "登录页面", auth: false },
  },
  {
    path: "/entpLogin",
    name: "entpLogin",
    component: () =>
      import(/* webpackChunkName: "login" */ "@/page/login/entpLogin.vue"),
    meta: { title: "运输企业登录", auth: false },
  },
  {
    path: "/personLogin",
    name: "personLogin",
    component: () =>
      import(/* webpackChunkName: "login" */ "@/page/login/personLogin.vue"),
    meta: { title: "驾押人员登录", auth: false },
  },
  {
    path: "/daily-report/index.html",
    name: "daily-report",
    component: () =>
      import(
        /* webpackChunkName: "login" */ "@/views/gov/report/daily-report.vue"
      ),
    meta: { title: "日报", auth: false },
  },
  {
    path: "/repor.html",
    name: "repor",
    component: () =>
      import(/* webpackChunkName: "login" */ "@/views/gov/report/repor.vue"),
    meta: { title: "月报", auth: false },
  },
  {
    path: "/404",
    name: "404",
    component: () =>
      import(/* webpackChunkName: "404" */ "@/page/error/404.vue"),
    meta: { title: "404错误", auth: false },
  },
  {
    path: "/video/video.html",
    name: "wb",
    component: () => import("@/views/video/index.vue"),
    meta: { title: "视频播放页面", auth: false },
  },
  {
    path: "/emergency/tracking.html",
    name: "emergency",
    component: () => import("@/views/gov/emergency/tracking.vue"),
    meta: { title: "应急救援-跟踪", auth: false },
  },
  {
    path: "/tracking.html",
    name: "tracking",
    component: () => import("@/views/gov/tracking/index.vue"),
    meta: { title: "跟踪", auth: false },
  },
  {
    path: "/nearbyvec.html",
    name: "nearbyvec",
    component: () => import("@/views/gov/nearbyvec/index.vue"),
    meta: { title: "附近车辆", auth: false },
  },
  {
    path: "/mobile-trace.html",
    name: "history",
    component: () => import("@/views/gov/history/index.vue"),
    meta: { title: "历史轨迹", auth: false },
  },
  {
    path: "/entp/roadPass",
    name: "roadPass",
    component: () => import("@/views/roadPass/index.vue"),
    meta: { title: "通行证详情", auth: false },
  },
];
// 企业账号路由
let entpRoutesData = [...entpRoutes];
// 驾押人员账号路由
let dirverRoutesData = [...dirverRoutes];
// 登记点路由
let ckRoutersData = [...ckRoutes];
// 装卸端路由
let cpRoutersData = [...cpRoutes];
// gov路由
let govRoutesData = [...govRoutes];

// function formatRoute(route, auth, type) {
//   route.forEach((r) => {
//     // r.meta = { ...(r.meta || {}), ...meta };
//     if (r.meta) {
//       r.meta.auth = auth;
//       r.meta.type = type;
//     } else {
//       r.meta = { auth, type };
//     }
//   });
//   if (route.children?.length) {
//     return formatRoute(route.children, auth, type);
//   } else {
//     return route;
//   }
// }
// 权限配置格式化
// formatRoute(defaultRoutes, false, "");
// formatRoute(ckRoutersData, false, "");
// formatRoute(entpRoutesData, true, 1);
// formatRoute(dirverRoutesData, true, 3);
// formatRoute(cpRoutersData, false, "");
// formatRoute(govRoutesData, false, "");

let allRoute = [
  ...defaultRoutes,
  ...ckRoutersData,
  ...entpRoutesData,
  ...dirverRoutesData,
  ...cpRoutersData,
  ...govRoutesData,
  ...[{ path: "*", redirect: { name: "404" } }],
];
const router = new VueRouter({
  mode: "history",
  base: process.env.BASE_URL,
  routes: allRoute,
});
console.log(router);

export default router;
