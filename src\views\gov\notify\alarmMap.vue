<template>
  <div class="page">
    <div id="map"></div>
    <div v-show="nogps" id="control_nogps" class="timelinePlay">无轨迹</div>
    <div v-show="loading" id="control_loading" class="timelinePlay">
      轨迹加载中
    </div>
    <div
      v-show="contorlShow"
      id="control"
      style="display: none"
      class="timelinePlay"
      @click="changeControl()"
    >
      <i
        :class="[isPlay ? 'fa-play' : 'fa-stop']"
        class="fa"
        aria-hidden="true"
      ></i>
      　播放轨迹(今日无轨迹则为昨日轨迹)
    </div>
  </div>
</template>

<script>
import * as $http from "@/apis/alarmMap";

var a = getUrlParms("a");
var c = getUrlParms("c") || "";
var t = getUrlParms("t") || new Date();
var p = getUrlParms("p");

var vecEnd = new BMap.Icon("./img/alarmMap/end.png", new BMap.Size(73, 73));
var vecStart = new BMap.Icon("./img/alarmMap/start.png", new BMap.Size(73, 73));
var vecAlarm = new BMap.Icon(
  "./img/alarmMap/map_alarm.png",
  new BMap.Size(73, 73)
);
var vecIcon = new BMap.Icon(
  "./img/alarmMap/_icon_r.png",
  new BMap.Size(73, 73)
);

function getUrlParms(name) {
  var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
  var r = window.location.search.substr(1).match(reg);
  if (r != null) return decodeURI(r[2]);
  return null;
}
export default {
  data() {
    return {
      map: null,
      traceCache: [], //返回轨迹值
      playTime: null,
      pointAlarm: [121.603046, 29.970938], //镇海经纬度
      pointStart: null,
      pointEnd: null,
      vecMarker: null,
      vecMarkerInfo: null,
      nowPointIndex: 0,

      nogps: false,
      loading: true,
      contorlShow: false,
      isPlay: true,
    };
  },
  created() {
    // let query = this.$route.query;
    // let vecNo = query.vecNo
  },
  mounted() {
    this.initMap();
  },
  methods: {
    // 初始化地图
    initMap() {
      this.map = new BMap.Map("map", {
        enableMapClick: false,
      });
      this.pointAlarm = new BMap.Point(this.pointAlarm[0], this.pointAlarm[1]);
      // this.pointAlarm = new BMap.Point(a.split(",")[0], a.split(",")[1]);

      this.map.centerAndZoom(this.pointAlarm, 12);
      this.map.enableScrollWheelZoom();

      var marker = new BMap.Marker(this.pointAlarm, { icon: vecAlarm });
      this.map.addOverlay(marker);
      this.traceGps();
    },
    traceGps() {
      let self = this;
      let param = {
        v: c,
        t: t,
      };

      $http.getMobileToday(param).then((res) => {
        if (res.code == 0 && res.data.length > 0) {
          self.traceCache = res.data;

          self.pointStart = new BMap.Point(
            self.traceCache[0].lonBd,
            self.traceCache[0].latBd
          );
          self.pointEnd = new BMap.Point(
            self.traceCache[self.traceCache.length - 1].lonBd,
            self.traceCache[self.traceCache.length - 1].latBd
          );

          clearInterval(self.playTime);
          self.map.clearOverlays();

          //轨迹分段处理
          let travels = [];
          let pois = res.data;
          if (pois && pois.length > 0) {
            let preTime = pois[pois.length - 1].updateTimeStamp;
            let diffTime = 0;
            // 倒序处理
            let tmpTravel = [];
            for (let i = pois.length - 1; i >= 0; i--) {
              let locTime = pois[i].updateTimeStamp;
              diffTime = locTime - preTime;
              // 两点之间相隔10分钟 进行分段处理
              if (!(diffTime <= 600)) {
                travels.push(tmpTravel);
                tmpTravel = [];
              }
              tmpTravel.push(pois[i]);
              preTime = locTime;
            }
            if (tmpTravel.length > 0) {
              travels.push(tmpTravel);
            }
          } else {
            this.loading = false;
            this.nogps = true;
            return;
          }

          //绘制轨迹
          let points = [];
          let track = "";
          res.data.map((item) => {
            points.push(item.lonBd + "," + item.latBd);
          });
          track = points.join(";");
          // console.log("points", points, track);
          let pointArray = [];
          let boundaries = [track];
          for (let i = 0; i < boundaries.length; i++) {
            let ply = new BMap.Polyline(boundaries[i], {
              strokeColor: "#0000ff",
              strokeOpacity: 0.6,
              strokeWeight: 5,
            }); //建立多边形覆盖物
            self.map.addOverlay(ply); //添加覆盖物
            pointArray = pointArray.concat(ply.getPath());
          }
          self.vecMarker = new BMap.Marker(
            new BMap.Point(
              travels[0][travels[0].length - 1].lonBd,
              travels[0][travels[0].length - 1].latBd
            ),
            { icon: vecIcon }
          );
          self.vecMarkerInfo = new BMap.Label(
            travels[0][travels[0].length - 1].updateTime,
            { offset: new BMap.Size(-20, 0) }
          );
          self.vecMarker.setLabel(self.vecMarkerInfo);
          self.map.addOverlay(self.vecMarker);
          let markerAlarm = new BMap.Marker(self.pointAlarm, {
            icon: vecAlarm,
            offset: new BMap.Size(20, 1),
          });
          self.map.addOverlay(markerAlarm);
          let markerStart = new BMap.Marker(self.pointStart, {
            icon: vecStart,
            offset: new BMap.Size(20, 1),
          });
          self.map.addOverlay(markerStart);
          let markerEnd = new BMap.Marker(self.pointEnd, {
            icon: vecEnd,
            offset: new BMap.Size(20, 1),
          });
          self.map.addOverlay(markerEnd);

          //绑定事件点击播放/暂停轨迹
          this.ready2Play();
        } else {
          this.loading = false;
          this.nogps = true;
        }
      });
    },
    // 轨迹播放逻辑
    tracePlay() {
      this.playTime = setInterval(() => {
        if (this.nowPointIndex >= this.traceCache.length) {
          clearInterval(this.playTime);
          this.isPlay = true;
          this.nowPointIndex = 0; //播放结束，下次再点击播放的话，从0开始
          return;
        }
        let point = new BMap.Point(
          this.traceCache[this.nowPointIndex].lonBd,
          this.traceCache[this.nowPointIndex].latBd
        );
        this.vecMarker.setPosition(point);
        this.vecMarker.setRotation(
          this.traceCache[this.nowPointIndex].direction - 90
        );
        this.vecMarkerInfo.setContent(
          this.traceCache[this.nowPointIndex].updateTime
        );
        this.map.setCenter(point);
        this.nowPointIndex++;
      }, 20);
    },

    ready2Play() {
      this.loading = false;
      this.contorlShow = true;
    },
    // 点击播放
    changeControl() {
      this.isPlay = !this.isPlay;
      if (this.isPlay) {
        clearInterval(this.playTime);
      } else {
        this.tracePlay();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.page {
  height: 100%;
}
#map {
  width: 100%;
  height: 100%;
  overflow: hidden;
  margin: 0;
  font-family: "微软雅黑";
}
.timelinePlay {
  position: fixed;
  bottom: 0;
  background: #667aff;
  height: 2.5em;
  font-size: 1em;
  line-height: 2.5em;
  display: block;
  color: #fff;
  text-align: center;
  width: 100%;
  cursor: pointer;
  z-index: 999;
}
.info {
  position: fixed;
  background: #667aff;
  height: 2.5em;
  font-size: 1em;
  line-height: 2.5em;
  display: block;
  color: #fff;
  text-align: center;
  width: 50%;
  z-index: 99;
  box-shadow: 0px 1px 5px #aaa;
  bottom: 0px;
}
</style>
